import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import Layout from '../../components/Layout';
import { AuthContext, supabase } from '../_app';
import { 
  generateMultipleExclusiveNCMKeys, 
  validateExclusiveNCMKey,
  getExclusiveKeyStats,
  batchValidateExclusiveNCMKeys
} from '../../lib/exclusiveNCM';

export default function AdminExclusiveNCM() {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [exclusiveKeys, setExclusiveKeys] = useState<any[]>([]);
  const [filteredKeys, setFilteredKeys] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterUsed, setFilterUsed] = useState<boolean | null>(null);
  const [generatingKeys, setGeneratingKeys] = useState(false);
  const [newKeysForm, setNewKeysForm] = useState({
    count: 10,
    minOnes: 3,
    maxOnes: 7,
  });
  const [generatedKeys, setGeneratedKeys] = useState<string[]>([]);

  useEffect(() => {
    // Check if user is authenticated and is admin
    const checkAdmin = async () => {
      if (!user) {
        router.push('/login');
        return;
      }

      try {
        // Check if user is admin
        const { data: memberData, error: memberError } = await supabase
          .from('members')
          .select('is_admin')
          .eq('auth_id', user.id)
          .single();

        if (memberError || !memberData?.is_admin) {
          router.push('/profile');
          return;
        }

        // Fetch exclusive NCM keys from Supabase
        const { data: keysData, error: keysError } = await supabase
          .from('ncm_keys')
          .select(`
            *,
            generated_by_member:members!generated_by_member_id(name),
            assigned_to_member:members!assigned_to_member_id(name)
          `)
          .eq('key_type', 'exclusive')
          .order('created_at', { ascending: false });

        if (keysError) {
          console.error('Error fetching exclusive NCM keys:', keysError);
          return;
        }

        setExclusiveKeys(keysData || []);
        setFilteredKeys(keysData || []);
      } catch (error) {
        console.error('Error checking admin status:', error);
        router.push('/profile');
      } finally {
        setLoading(false);
      }
    };

    checkAdmin();
  }, [user, router]);

  // Apply filters when search term or filters change
  useEffect(() => {
    let filtered = [...exclusiveKeys];
    
    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(key => 
        key.key.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Apply used filter
    if (filterUsed !== null) {
      filtered = filtered.filter(key => key.used === filterUsed);
    }
    
    setFilteredKeys(filtered);
  }, [searchTerm, filterUsed, exclusiveKeys]);

  const handleGenerateKeys = async () => {
    try {
      setGeneratingKeys(true);
      
      // Generate new exclusive keys with constraints
      const newKeys = generateMultipleExclusiveNCMKeys(newKeysForm.count);
      
      // Validate all generated keys
      const validationResults = batchValidateExclusiveNCMKeys(newKeys);
      const validKeys = validationResults.filter(result => result.isValid).map(result => result.key);
      
      if (validKeys.length === 0) {
        throw new Error('No valid keys were generated');
      }
      
      // Prepare keys data for database
      const keysData = validKeys.map(key => ({
        key,
        key_type: 'exclusive',
        level: 1, // Exclusive members don't have traditional levels, but we set to 1 for consistency
        generated_by_member_id: null, // Admin-generated
        assigned_to_member_id: null,
        used: false,
        created_at: new Date().toISOString(),
        used_at: null,
      }));
      
      // Add to Supabase
      const { data, error } = await supabase
        .from('ncm_keys')
        .insert(keysData)
        .select();
        
      if (error) {
        throw new Error('Error creating exclusive NCM keys');
      }
      
      // Update state with new keys
      setExclusiveKeys(prev => [...(data || []), ...prev]);
      setGeneratedKeys(validKeys);
      
      // Reset form
      setNewKeysForm({
        count: 10,
        minOnes: 3,
        maxOnes: 7,
      });
      
      alert(`${validKeys.length} clés NCM exclusives ont été générées avec succès.`);
    } catch (error) {
      console.error('Error generating exclusive keys:', error);
      alert('Erreur lors de la génération des clés NCM exclusives.');
    } finally {
      setGeneratingKeys(false);
    }
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewKeysForm(prev => ({
      ...prev,
      [name]: parseInt(value),
    }));
  };

  const handleCopyKeys = (keys: string[]) => {
    navigator.clipboard.writeText(keys.join('\n'));
    alert('Clés copiées dans le presse-papier');
  };

  const getKeyStats = (key: string) => {
    return getExclusiveKeyStats(key);
  };

  if (loading) {
    return (
      <Layout title="Gestion des NCM Exclusifs | IAFUL Admin" isAdmin>
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center h-64">
            <div className="text-gold-luxurious text-xl">Chargement...</div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Gestion des NCM Exclusifs | IAFUL Admin" isAdmin>
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-playfair font-bold text-gold-luxurious mb-8">
            Gestion des NCM Exclusifs
          </h1>
          <p className="text-white-off/80 mb-8">
            Génération et gestion des clés NCM pour les membres exclusifs. 
            Ces clés utilisent un format sécurisé avec 9 caractères binaires et checksum.
          </p>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            <div className="md:col-span-1">
              <div className="card">
                <h2 className="text-xl font-playfair text-gold-luxurious mb-6">Générer des NCM Exclusifs</h2>
                
                <div className="space-y-4">
                  <div>
                    <label htmlFor="count" className="block text-white-off mb-2">
                      Nombre de clés
                    </label>
                    <input
                      type="number"
                      id="count"
                      name="count"
                      value={newKeysForm.count}
                      onChange={handleFormChange}
                      min={1}
                      max={100}
                      className="input-field w-full"
                    />
                  </div>
                  
                  <div className="bg-gray-anthracite/30 p-4 rounded-md">
                    <h3 className="text-gold-luxurious text-sm font-medium mb-2">Format des clés:</h3>
                    <p className="text-white-off/70 text-xs mb-2">EXC-XXXXXXXXX-C</p>
                    <ul className="text-white-off/60 text-xs space-y-1">
                      <li>• EXC: Préfixe exclusif</li>
                      <li>• 9 caractères binaires (0/1)</li>
                      <li>• 1 caractère checksum (0-F)</li>
                      <li>• Sécurité anti-piratage</li>
                    </ul>
                  </div>
                  
                  <button
                    onClick={handleGenerateKeys}
                    disabled={generatingKeys}
                    className="btn w-full py-3"
                  >
                    {generatingKeys ? 'Génération en cours...' : 'Générer des NCM Exclusifs'}
                  </button>
                </div>
              </div>
            </div>
            
            <div className="md:col-span-2">
              <div className="card">
                <h2 className="text-xl font-playfair text-gold-luxurious mb-6">Statistiques des NCM Exclusifs</h2>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="bg-gray-anthracite p-4 rounded-md">
                    <div className="text-white-off/70 text-sm">Total Exclusifs</div>
                    <div className="text-gold-luxurious text-2xl font-bold">{exclusiveKeys.length}</div>
                  </div>
                  
                  <div className="bg-gray-anthracite p-4 rounded-md">
                    <div className="text-white-off/70 text-sm">Utilisés</div>
                    <div className="text-gold-luxurious text-2xl font-bold">
                      {exclusiveKeys.filter(key => key.used).length}
                    </div>
                  </div>
                  
                  <div className="bg-gray-anthracite p-4 rounded-md">
                    <div className="text-white-off/70 text-sm">Disponibles</div>
                    <div className="text-gold-luxurious text-2xl font-bold">
                      {exclusiveKeys.filter(key => !key.used).length}
                    </div>
                  </div>
                  
                  <div className="bg-gray-anthracite p-4 rounded-md">
                    <div className="text-white-off/70 text-sm">Taux d'utilisation</div>
                    <div className="text-gold-luxurious text-2xl font-bold">
                      {exclusiveKeys.length > 0 
                        ? `${Math.round((exclusiveKeys.filter(key => key.used).length / exclusiveKeys.length) * 100)}%` 
                        : '0%'}
                    </div>
                  </div>
                </div>

                {generatedKeys.length > 0 && (
                  <div className="bg-gradient-to-r from-gold-luxurious/10 to-gold-warm/10 border border-gold-luxurious/30 rounded-md p-4 mb-6">
                    <h3 className="text-gold-luxurious font-medium mb-2">Dernières clés générées:</h3>
                    <div className="space-y-1">
                      {generatedKeys.slice(0, 5).map((key, index) => (
                        <div key={index} className="text-white-off font-mono text-sm">{key}</div>
                      ))}
                      {generatedKeys.length > 5 && (
                        <div className="text-white-off/70 text-xs">
                          ... et {generatedKeys.length - 5} autres
                        </div>
                      )}
                    </div>
                    <button
                      onClick={() => handleCopyKeys(generatedKeys)}
                      className="text-gold-luxurious hover:underline text-sm mt-2"
                    >
                      Copier toutes les clés
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
              <h2 className="text-xl font-playfair text-gold-luxurious mb-4 md:mb-0">Liste des NCM Exclusifs</h2>
              
              <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 w-full md:w-auto">
                <input
                  type="text"
                  placeholder="Rechercher un NCM exclusif..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input-field w-full md:w-64"
                />
                
                <select
                  value={filterUsed === null ? '' : filterUsed ? 'true' : 'false'}
                  onChange={(e) => setFilterUsed(e.target.value === '' ? null : e.target.value === 'true')}
                  className="input-field w-full md:w-40"
                >
                  <option value="">Tous les statuts</option>
                  <option value="true">Utilisés</option>
                  <option value="false">Disponibles</option>
                </select>
              </div>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gold-luxurious/30">
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">NCM Exclusif</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Statut</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Utilisé par</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Date de création</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Validation</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredKeys.slice(0, 20).map((key) => {
                    const stats = getKeyStats(key.key);
                    return (
                      <tr key={key.id} className="border-b border-gray-anthracite">
                        <td className="py-3 px-4 text-white-off font-mono text-sm">{key.key}</td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded-full text-xs ${key.used ? 'bg-green-900/30 text-green-400' : 'bg-yellow-900/30 text-yellow-400'}`}>
                            {key.used ? 'Utilisé' : 'Disponible'}
                          </span>
                        </td>
                        <td className="py-3 px-4 text-white-off">
                          {key.assigned_to_member?.name || '-'}
                        </td>
                        <td className="py-3 px-4 text-white-off">
                          {new Date(key.created_at).toLocaleDateString()}
                        </td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded-full text-xs ${stats.isValid ? 'bg-green-900/30 text-green-400' : 'bg-red-900/30 text-red-400'}`}>
                            {stats.isValid ? 'Valide' : 'Invalide'}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          <button
                            onClick={() => handleCopyKeys([key.key])}
                            className="text-gold-luxurious hover:underline"
                          >
                            Copier
                          </button>
                        </td>
                      </tr>
                    );
                  })}
                  
                  {filteredKeys.length === 0 && (
                    <tr>
                      <td colSpan={6} className="py-4 text-center text-white-off/70">
                        Aucun NCM exclusif trouvé
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
            
            {filteredKeys.length > 20 && (
              <div className="mt-4 text-center text-white-off/70">
                Affichage des 20 premiers résultats sur {filteredKeys.length}
              </div>
            )}
            
            {filteredKeys.filter(key => !key.used).length > 0 && (
              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => handleCopyKeys(filteredKeys.filter(key => !key.used).map(key => key.key))}
                  className="btn px-4 py-2"
                >
                  Copier tous les NCM exclusifs disponibles
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
}
