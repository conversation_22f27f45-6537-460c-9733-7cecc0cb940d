import Link from 'next/link';
import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import { AuthContext, supabase } from '../_app';
import Layout from '../../components/Layout';

export default function AdminDashboard() {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalMembers: 0,
    totalProducts: 0,
    totalSales: 0,
    pendingPayments: 0,
  });

  useEffect(() => {
    // Check if user is authenticated and is admin
    const checkAdmin = async () => {
      if (!user) {
        router.push('/login');
        return;
      }

      try {
        // Check if user is admin
        const { data, error } = await supabase
          .from('members')
          .select('is_admin')
          .eq('auth_id', user.id)
          .single();

        if (error || !data?.is_admin) {
          // Not an admin, redirect to profile
          router.push('/profile');
          return;
        }

        // Fetch dashboard stats from the database
        try {
          // Get total members count
          const { count: membersCount } = await supabase
            .from('members')
            .select('*', { count: 'exact', head: true });

          // Get total products count
          const { count: productsCount } = await supabase
            .from('products')
            .select('*', { count: 'exact', head: true });

          // Set the stats with real data
          setStats({
            totalMembers: membersCount || 0,
            totalProducts: productsCount || 0,
            totalSales: 0, // This would need a sales table
            pendingPayments: 0, // This would need a payments table
          });
        } catch (statsError) {
          console.error('Error fetching stats:', statsError);
          // Set default values if there's an error
          setStats({
            totalMembers: 0,
            totalProducts: 0,
            totalSales: 0,
            pendingPayments: 0,
          });
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAdmin();
  }, [user, router]);

  if (loading) {
    return (
      <Layout title="Chargement... | IAFUL Admin" isAdmin>
        <div className="flex items-center justify-center h-64">
          <div className="text-gold-luxurious text-xl">Chargement...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Admin Dashboard | IAFUL" isAdmin>
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-playfair font-bold text-gold-luxurious mb-8">
          Dashboard Administrateur
        </h1>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <div className="card">
              <h3 className="text-xl font-playfair text-gold-luxurious mb-2">Membres</h3>
              <p className="text-3xl text-white-off font-bold">{stats.totalMembers}</p>
              <Link href="/admin/members" className="text-gold-luxurious hover:underline text-sm mt-2 inline-block">
                Gérer les membres &rarr;
              </Link>
            </div>

            <div className="card">
              <h3 className="text-xl font-playfair text-gold-luxurious mb-2">Produits</h3>
              <p className="text-3xl text-white-off font-bold">{stats.totalProducts}</p>
              <Link href="/admin/products" className="text-gold-luxurious hover:underline text-sm mt-2 inline-block">
                Gérer les produits &rarr;
              </Link>
            </div>

            <div className="card">
              <h3 className="text-xl font-playfair text-gold-luxurious mb-2">Ventes</h3>
              <p className="text-3xl text-white-off font-bold">{stats.totalSales}</p>
              <Link href="/admin/sales" className="text-gold-luxurious hover:underline text-sm mt-2 inline-block">
                Voir les ventes &rarr;
              </Link>
            </div>

            <div className="card">
              <h3 className="text-xl font-playfair text-gold-luxurious mb-2">Paiements en attente</h3>
              <p className="text-3xl text-white-off font-bold">{stats.pendingPayments}</p>
              <Link href="/admin/payments" className="text-gold-luxurious hover:underline text-sm mt-2 inline-block">
                Gérer les paiements &rarr;
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div className="card">
              <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Actions rapides</h2>
              <div className="space-y-4">
                <Link href="/admin/generate-ncm" className="btn w-full py-3 flex justify-center">
                  Générer des NCM
                </Link>
                <Link href="/admin/add-product" className="btn w-full py-3 flex justify-center">
                  Ajouter un produit
                </Link>
                <Link href="/admin/settings" className="btn w-full py-3 flex justify-center">
                  Paramètres
                </Link>
              </div>
            </div>

            <div className="card">
              <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Structure des membres</h2>
              <p className="text-white-off mb-4">
                Visualisez la structure pyramidale complète des membres.
              </p>
              <div className="bg-gray-anthracite border border-gold-luxurious/30 rounded-md p-4 h-48 flex items-center justify-center">
                <p className="text-white-off/70">
                  La visualisation de la structure sera implémentée ici avec React Flow.
                </p>
              </div>
              <div className="mt-4">
                <Link href="/admin/member-tree" className="text-gold-luxurious hover:underline">
                  Voir la structure complète &rarr;
                </Link>
              </div>
            </div>
          </div>

          <div className="card">
            <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Dernières activités</h2>
            <div className="space-y-4">
              <div className="p-3 bg-gray-anthracite rounded-md">
                <p className="text-white-off">Nouveau membre inscrit: <span className="text-gold-luxurious">Jean Dupont</span></p>
                <p className="text-white-off/70 text-sm">Il y a 2 heures</p>
              </div>
              <div className="p-3 bg-gray-anthracite rounded-md">
                <p className="text-white-off">Nouvelle vente: <span className="text-gold-luxurious">Royal OG</span></p>
                <p className="text-white-off/70 text-sm">Il y a 5 heures</p>
              </div>
              <div className="p-3 bg-gray-anthracite rounded-md">
                <p className="text-white-off">Paiement confirmé: <span className="text-gold-luxurious">0.0032 BTC</span></p>
                <p className="text-white-off/70 text-sm">Il y a 1 jour</p>
              </div>
            </div>
          </div>
        </div>
    </Layout>
  );
}
