import Link from 'next/link';
import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import { AuthContext, supabase } from '../_app';
import Layout from '../../components/Layout';

export default function AdminDashboard() {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalMembers: 0,
    totalProducts: 0,
    totalSales: 0,
    pendingPayments: 0,
  });

  useEffect(() => {
    // Check if user is authenticated and is admin
    const checkAdmin = async () => {
      if (!user) {
        router.push('/login');
        return;
      }

      try {
        // Check if user is admin
        const { data, error } = await supabase
          .from('members')
          .select('is_admin')
          .eq('auth_id', user.id)
          .single();

        if (error || !data?.is_admin) {
          // Not an admin, redirect to profile
          router.push('/profile');
          return;
        }

        // Fetch dashboard stats from the database
        try {
          // Get total members count
          const { count: membersCount } = await supabase
            .from('members')
            .select('*', { count: 'exact', head: true });

          // Get total products count
          const { count: productsCount } = await supabase
            .from('products')
            .select('*', { count: 'exact', head: true });

          // Set the stats with real data
          setStats({
            totalMembers: membersCount || 0,
            totalProducts: productsCount || 0,
            totalSales: 0, // This would need a sales table
            pendingPayments: 0, // This would need a payments table
          });
        } catch (statsError) {
          console.error('Error fetching stats:', statsError);
          // Set default values if there's an error
          setStats({
            totalMembers: 0,
            totalProducts: 0,
            totalSales: 0,
            pendingPayments: 0,
          });
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
      } finally {
        setLoading(false);
      }
    };

    checkAdmin();
  }, [user, router]);

  if (loading) {
    return (
      <Layout title="Chargement... | IAFUL Admin" isAdmin>
        <div className="flex items-center justify-center h-64">
          <div className="text-gold-luxurious text-xl">Chargement...</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Admin Dashboard | IAFUL" isAdmin>
      <div className="container mx-auto px-4">
        <h1 className="text-3xl font-playfair font-bold text-gold-luxurious mb-8">
          Dashboard Administrateur
        </h1>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-12">
            <div className="card">
              <h3 className="text-xl font-playfair text-gold-luxurious mb-2">Membres</h3>
              <p className="text-3xl text-white-off font-bold">{stats.totalMembers}</p>
              <Link href="/admin/members" className="text-gold-luxurious hover:underline text-sm mt-2 inline-block">
                Gérer les membres &rarr;
              </Link>
            </div>

            <div className="card">
              <h3 className="text-xl font-playfair text-gold-luxurious mb-2">Produits</h3>
              <p className="text-3xl text-white-off font-bold">{stats.totalProducts}</p>
              <Link href="/admin/products" className="text-gold-luxurious hover:underline text-sm mt-2 inline-block">
                Gérer les produits &rarr;
              </Link>
            </div>

            <div className="card">
              <h3 className="text-xl font-playfair text-gold-luxurious mb-2">Ventes</h3>
              <p className="text-3xl text-white-off font-bold">{stats.totalSales}</p>
              <Link href="/admin/sales" className="text-gold-luxurious hover:underline text-sm mt-2 inline-block">
                Voir les ventes &rarr;
              </Link>
            </div>

            <div className="card">
              <h3 className="text-xl font-playfair text-gold-luxurious mb-2">Paiements en attente</h3>
              <p className="text-3xl text-white-off font-bold">{stats.pendingPayments}</p>
              <Link href="/admin/payments" className="text-gold-luxurious hover:underline text-sm mt-2 inline-block">
                Gérer les paiements &rarr;
              </Link>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12">
            <div className="card">
              <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Actions rapides</h2>
              <div className="space-y-4">
                <Link href="/admin/generate-ncm" className="btn w-full py-3 flex justify-center">
                  Générer des NCM Standard
                </Link>
                <Link href="/admin/exclusive-ncm" className="btn w-full py-3 flex justify-center bg-gradient-to-r from-gold-luxurious to-gold-warm text-black-deep hover:shadow-lg hover:shadow-gold-luxurious/25">
                  <span className="flex items-center">
                    Générer des NCM Exclusifs
                    <span className="ml-2 px-2 py-1 bg-black-deep/20 text-black-deep text-xs rounded">
                      Premium
                    </span>
                  </span>
                </Link>
                <Link href="/admin/ncm-keys" className="btn w-full py-3 flex justify-center">
                  Gérer tous les NCM
                </Link>
                <Link href="/admin/add-product" className="btn w-full py-3 flex justify-center">
                  Ajouter un produit
                </Link>
                <Link href="/admin/settings" className="btn w-full py-3 flex justify-center bg-gradient-to-r from-blue-600 to-blue-700 text-white hover:shadow-lg hover:shadow-blue-600/25">
                  <span className="flex items-center">
                    ⚙️ Paramètres Admin
                    <span className="ml-2 px-2 py-1 bg-white/20 text-white text-xs rounded">
                      Sécurité
                    </span>
                  </span>
                </Link>
              </div>
            </div>

            <div className="card">
              <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Structure des membres</h2>
              <p className="text-white-off mb-4">
                Visualisez la structure pyramidale complète des membres.
              </p>
              <div className="bg-gray-anthracite border border-gold-luxurious/30 rounded-md p-4 h-48 flex items-center justify-center">
                <p className="text-white-off/70">
                  La visualisation de la structure sera implémentée ici avec React Flow.
                </p>
              </div>
              <div className="mt-4">
                <Link href="/admin/member-tree" className="text-gold-luxurious hover:underline">
                  Voir la structure complète &rarr;
                </Link>
              </div>
            </div>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-12">
            <div className="lg:col-span-2">
              <div className="card">
                <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Dernières activités</h2>
            <div className="space-y-4">
              <div className="p-3 bg-gray-anthracite rounded-md">
                <p className="text-white-off">Nouveau membre inscrit: <span className="text-gold-luxurious">Jean Dupont</span></p>
                <p className="text-white-off/70 text-sm">Il y a 2 heures</p>
              </div>
              <div className="p-3 bg-gray-anthracite rounded-md">
                <p className="text-white-off">Nouvelle vente: <span className="text-gold-luxurious">Royal OG</span></p>
                <p className="text-white-off/70 text-sm">Il y a 5 heures</p>
              </div>
              <div className="p-3 bg-gray-anthracite rounded-md">
                <p className="text-white-off">Paiement confirmé: <span className="text-gold-luxurious">0.0032 BTC</span></p>
                <p className="text-white-off/70 text-sm">Il y a 1 jour</p>
              </div>
            </div>
              </div>
            </div>

            <div className="lg:col-span-1">
              <div className="card">
                <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Admin Info</h2>
                <div className="space-y-4">
                  <div className="bg-gradient-to-r from-blue-600/10 to-blue-700/10 border border-blue-600/30 rounded-md p-4">
                    <div className="flex items-center mb-3">
                      <div className="w-10 h-10 bg-gradient-to-r from-blue-600 to-blue-700 rounded-full flex items-center justify-center text-white font-bold">
                        H
                      </div>
                      <div className="ml-3">
                        <div className="text-white-off font-medium">Hassen Admin</div>
                        <div className="text-white-off/70 text-sm"><EMAIL></div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="px-2 py-1 bg-blue-600/20 text-blue-400 text-xs rounded">
                        Administrateur
                      </span>
                      <span className="text-green-400 text-xs">● En ligne</span>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Link href="/admin/settings" className="block w-full text-center py-2 px-4 bg-gradient-to-r from-blue-600 to-blue-700 text-white rounded-md hover:shadow-lg hover:shadow-blue-600/25 transition-all">
                      ⚙️ Gérer le compte
                    </Link>
                    <button
                      onClick={async () => {
                        await supabase.auth.signOut();
                        router.push('/');
                      }}
                      className="block w-full text-center py-2 px-4 bg-gray-anthracite text-white-off rounded-md hover:bg-gray-anthracite/80 transition-all"
                    >
                      🚪 Se déconnecter
                    </button>
                  </div>

                  <div className="bg-gray-anthracite/30 p-3 rounded-md">
                    <div className="text-white-off/70 text-xs mb-1">Dernière connexion</div>
                    <div className="text-white-off text-sm">Aujourd'hui à 14:30</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
    </Layout>
  );
}
