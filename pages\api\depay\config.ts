import { NextApiRequest, NextApiResponse } from 'next';
import { verify } from '@depay/js-verify-signature';
import { supabase } from '../../../lib/supabase';
import crypto from 'crypto';

// DePay dynamic configuration endpoint
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify the request signature from DePay
    const signature = req.headers['x-signature'] as string;
    const publicKey = process.env.DEPAY_PUBLIC_KEY;

    if (!publicKey) {
      console.error('DEPAY_PUBLIC_KEY not configured');
      return res.status(500).json({ error: 'Payment system configuration error' });
    }

    // Verify request authenticity
    const verified = await verify({
      signature,
      data: JSON.stringify(req.body),
      publicKey,
    });

    if (!verified) {
      console.error('Invalid DePay signature');
      return res.status(401).json({ error: 'Invalid signature' });
    }

    // Extract payload data
    const { order_id, member_id, product_id, discount_id } = req.body;

    if (!order_id || !product_id) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    // Fetch order details
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select('*')
      .eq('id', order_id)
      .single();

    if (orderError || !order) {
      return res.status(404).json({ error: 'Order not found' });
    }

    // Fetch product details
    const { data: product, error: productError } = await supabase
      .from('products')
      .select('*')
      .eq('id', product_id)
      .single();

    if (productError || !product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Calculate final price (with discount if applicable)
    let finalPrice = order.amount_eur;
    
    if (discount_id) {
      const { data: discount, error: discountError } = await supabase
        .from('coupons')
        .select('*')
        .eq('id', discount_id)
        .eq('used', false)
        .single();

      if (!discountError && discount) {
        const discountAmount = (product.price * discount.discount_pct) / 100;
        finalPrice = product.price - discountAmount;
        
        // Update order with discount
        await supabase
          .from('orders')
          .update({ 
            amount_eur: finalPrice,
            discount_applied: discount.id 
          })
          .eq('id', order_id);
      }
    }

    // Create dynamic configuration for DePay
    const configuration = {
      amount: {
        currency: 'EUR',
        fix: finalPrice
      },
      accept: [
        // USDT on Ethereum
        {
          blockchain: 'ethereum',
          token: '******************************************',
          receiver: process.env.ETHEREUM_WALLET_ADDRESS
        },
        // ETH (native token)
        {
          blockchain: 'ethereum',
          token: '******************************************',
          receiver: process.env.ETHEREUM_WALLET_ADDRESS
        },
        // USDC on Ethereum
        {
          blockchain: 'ethereum',
          token: '******************************************',
          receiver: process.env.ETHEREUM_WALLET_ADDRESS
        },
        // USDT on Polygon
        {
          blockchain: 'polygon',
          token: '******************************************',
          receiver: process.env.POLYGON_WALLET_ADDRESS
        },
        // MATIC (native token)
        {
          blockchain: 'polygon',
          token: '******************************************',
          receiver: process.env.POLYGON_WALLET_ADDRESS
        }
      ],
      // Forward to success page after payment
      forward_to: `${process.env.NEXT_PUBLIC_BASE_URL}/payment/success?order=${order_id}`
    };

    // Sign the response
    const privateKey = process.env.DEPAY_PRIVATE_KEY;
    if (!privateKey) {
      console.error('DEPAY_PRIVATE_KEY not configured');
      return res.status(500).json({ error: 'Payment system configuration error' });
    }

    const dataToSign = JSON.stringify(configuration);
    const signature_response = crypto.sign('sha256', Buffer.from(dataToSign), {
      key: privateKey,
      padding: crypto.constants.RSA_PKCS1_PSS_PADDING,
      saltLength: 64,
    });

    const urlSafeBase64Signature = signature_response
      .toString('base64')
      .replace('+', '-')
      .replace('/', '_')
      .replace(/=+$/, '');

    // Set signature header
    res.setHeader('x-signature', urlSafeBase64Signature);
    res.setHeader('Content-Type', 'application/json');

    // Return configuration
    return res.status(200).json(configuration);

  } catch (error) {
    console.error('DePay config error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

// Disable body parsing to get raw body for signature verification
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
