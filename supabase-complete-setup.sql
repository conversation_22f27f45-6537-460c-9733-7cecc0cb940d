-- IAFUL Complete Database Setup for Supabase
-- Execute this in your Supabase SQL Editor

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- ===========================================
-- MEMBERS TABLE
-- ===========================================
CREATE TABLE IF NOT EXISTS members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    auth_id UUID UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    level INTEGER DEFAULT 1,
    referrer_id UUID REFERENCES members(id),
    ncm_key_used VARCHAR(50),
    is_admin BOOLEAN DEFAULT FALSE,
    is_exclusive BOOLEAN DEFAULT FALSE,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===========================================
-- PRODUCTS TABLE
-- ===========================================
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    image_url TEXT,
    category VARCHAR(100),
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert IAFUL products
INSERT INTO products (name, description, price, category) VALUES
    ('Le Jaune', 'Une variété premium aux arômes citronnés et aux effets énergisants. Cultivée avec soin pour offrir une expérience gustative exceptionnelle.', 10.00, 'premium'),
    ('Le Mousseux', 'Variété légère et pétillante, parfaite pour une détente en douceur. Notes florales et effet relaxant garanti.', 5.00, 'standard'),
    ('La California', 'Directement inspirée des meilleures variétés californiennes. Puissante et savoureuse, pour les connaisseurs exigeants.', 10.00, 'premium'),
    ('L''Amnesia', 'Classique incontournable aux effets puissants et durables. Arômes complexes et expérience mémorable assurée.', 8.00, 'classic')
ON CONFLICT (name) DO NOTHING;

-- ===========================================
-- NCM KEYS TABLE
-- ===========================================
CREATE TABLE IF NOT EXISTS ncm_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(50) UNIQUE NOT NULL,
    key_type VARCHAR(20) DEFAULT 'standard' CHECK (key_type IN ('standard', 'exclusive')),
    level INTEGER DEFAULT 1,
    generated_by_member_id UUID REFERENCES members(id),
    assigned_to_member_id UUID REFERENCES members(auth_id),
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    used_at TIMESTAMP WITH TIME ZONE
);

-- ===========================================
-- ORDERS TABLE (DePay Integration)
-- ===========================================
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    member_id UUID REFERENCES members(auth_id),
    product_id UUID REFERENCES products(id),
    amount_eur DECIMAL(10,2) NOT NULL,
    amount_crypto DECIMAL(18,8),
    token_address VARCHAR(42),
    blockchain VARCHAR(20),
    transaction_hash VARCHAR(66),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'fulfilled', 'failed', 'cancelled')),
    discount_applied UUID,
    payment_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    paid_at TIMESTAMP WITH TIME ZONE,
    fulfilled_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===========================================
-- PAYMENT LOGS TABLE
-- ===========================================
CREATE TABLE IF NOT EXISTS payment_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id),
    event_type VARCHAR(20) NOT NULL CHECK (event_type IN ('created', 'attempt', 'processing', 'succeeded', 'failed', 'fulfillment_error')),
    transaction_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===========================================
-- COUPONS TABLE (Exclusive Members)
-- ===========================================
CREATE TABLE IF NOT EXISTS coupons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL,
    discount_pct INTEGER NOT NULL CHECK (discount_pct > 0 AND discount_pct <= 100),
    assigned_to_member_id UUID REFERENCES members(auth_id),
    used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMP WITH TIME ZONE,
    used_by_order_id UUID REFERENCES orders(id),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===========================================
-- MEMBER PRODUCT ACCESS TABLE
-- ===========================================
CREATE TABLE IF NOT EXISTS member_product_access (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    member_id UUID REFERENCES members(auth_id),
    product_id UUID REFERENCES products(id),
    order_id UUID REFERENCES orders(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- ===========================================
-- INDEXES FOR PERFORMANCE
-- ===========================================
CREATE INDEX IF NOT EXISTS idx_members_auth_id ON members(auth_id);
CREATE INDEX IF NOT EXISTS idx_members_email ON members(email);
CREATE INDEX IF NOT EXISTS idx_members_is_admin ON members(is_admin);
CREATE INDEX IF NOT EXISTS idx_members_is_exclusive ON members(is_exclusive);

CREATE INDEX IF NOT EXISTS idx_ncm_keys_key ON ncm_keys(key);
CREATE INDEX IF NOT EXISTS idx_ncm_keys_used ON ncm_keys(used);
CREATE INDEX IF NOT EXISTS idx_ncm_keys_type ON ncm_keys(key_type);

CREATE INDEX IF NOT EXISTS idx_orders_member_id ON orders(member_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);

CREATE INDEX IF NOT EXISTS idx_coupons_code ON coupons(code);
CREATE INDEX IF NOT EXISTS idx_coupons_assigned_to_member_id ON coupons(assigned_to_member_id);
CREATE INDEX IF NOT EXISTS idx_coupons_used ON coupons(used);

-- ===========================================
-- ROW LEVEL SECURITY (RLS)
-- ===========================================
ALTER TABLE members ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE coupons ENABLE ROW LEVEL SECURITY;
ALTER TABLE member_product_access ENABLE ROW LEVEL SECURITY;

-- Members can view/update their own data
CREATE POLICY "Users can view own member data" ON members
    FOR SELECT USING (auth.uid() = auth_id);

CREATE POLICY "Users can update own member data" ON members
    FOR UPDATE USING (auth.uid() = auth_id);

-- Admins can manage all members
CREATE POLICY "Admins can manage all members" ON members
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM members admin_member 
            WHERE admin_member.auth_id = auth.uid() 
            AND admin_member.is_admin = TRUE
        )
    );

-- Orders policies
CREATE POLICY "Users can view own orders" ON orders
    FOR SELECT USING (auth.uid() = member_id);

CREATE POLICY "Admins can manage all orders" ON orders
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM members admin_member 
            WHERE admin_member.auth_id = auth.uid() 
            AND admin_member.is_admin = TRUE
        )
    );

-- Coupons policies
CREATE POLICY "Users can view own coupons" ON coupons
    FOR SELECT USING (auth.uid() = assigned_to_member_id);

CREATE POLICY "Admins can manage all coupons" ON coupons
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM members admin_member 
            WHERE admin_member.auth_id = auth.uid() 
            AND admin_member.is_admin = TRUE
        )
    );

-- ===========================================
-- SAMPLE DATA FOR TESTING
-- ===========================================

-- Insert sample exclusive coupons
INSERT INTO coupons (code, discount_pct, expires_at) VALUES
    ('EXCLUSIVE10', 10, NOW() + INTERVAL '1 year'),
    ('EXCLUSIVE15', 15, NOW() + INTERVAL '1 year'),
    ('EXCLUSIVE20', 20, NOW() + INTERVAL '1 year'),
    ('VIP25', 25, NOW() + INTERVAL '1 year'),
    ('PREMIUM30', 30, NOW() + INTERVAL '1 year')
ON CONFLICT (code) DO NOTHING;

-- ===========================================
-- FUNCTIONS AND TRIGGERS
-- ===========================================

-- Update timestamp function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add update triggers
CREATE TRIGGER update_members_updated_at 
    BEFORE UPDATE ON members 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at 
    BEFORE UPDATE ON products 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_orders_updated_at 
    BEFORE UPDATE ON orders 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_coupons_updated_at 
    BEFORE UPDATE ON coupons 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- ===========================================
-- SUCCESS MESSAGE
-- ===========================================
SELECT 'IAFUL database setup completed successfully!' as message,
       'Tables created: members, products, ncm_keys, orders, payment_logs, coupons, member_product_access' as tables_created,
       'Sample products and coupons inserted' as sample_data,
       'RLS policies configured for security' as security;
