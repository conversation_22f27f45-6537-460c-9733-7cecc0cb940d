import { useState } from 'react';
import { mockDB } from '../lib/mockAuth';

export default function SimpleLoginTest() {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testLogin = async (email: string, password: string) => {
    setLoading(true);
    setResult('');
    
    try {
      console.log(`Testing login with: ${email} / ${password}`);
      setResult(prev => prev + `Testing login with: ${email} / ${password}\n`);
      
      // Test direct mock DB call
      const loginResult = await mockDB.signInWithPassword(email, password);
      console.log('Login result:', loginResult);
      setResult(prev => prev + `Login result: ${JSON.stringify(loginResult, null, 2)}\n`);
      
      if (loginResult.data.user) {
        // Test member lookup
        const member = await mockDB.getMemberById(loginResult.data.user.id);
        console.log('Member data:', member);
        setResult(prev => prev + `Member data: ${JSON.stringify(member, null, 2)}\n`);
        
        setResult(prev => prev + `✅ Login successful for ${email}\n\n`);
      }
    } catch (error) {
      console.error('Login error:', error);
      setResult(prev => prev + `❌ Login failed: ${error}\n\n`);
    } finally {
      setLoading(false);
    }
  };

  const testAllCredentials = async () => {
    const credentials = [
      { email: '<EMAIL>', password: 'admin123' },
      { email: '<EMAIL>', password: 'member123' },
      { email: '<EMAIL>', password: 'member123' },
      { email: '<EMAIL>', password: 'member123' },
      { email: '<EMAIL>', password: 'wrong' }
    ];

    for (const cred of credentials) {
      await testLogin(cred.email, cred.password);
    }
  };

  return (
    <div className="min-h-screen bg-black-deep p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gold-luxurious mb-8">Simple Login Test</h1>
        
        <div className="card mb-8">
          <h2 className="text-xl text-gold-luxurious mb-4">Test Individual Credentials</h2>
          <div className="grid grid-cols-2 gap-4">
            <button
              onClick={() => testLogin('<EMAIL>', 'admin123')}
              disabled={loading}
              className="btn"
            >
              Test Admin Login
            </button>
            <button
              onClick={() => testLogin('<EMAIL>', 'member123')}
              disabled={loading}
              className="btn"
            >
              Test Member Login
            </button>
            <button
              onClick={() => testLogin('<EMAIL>', 'wrong')}
              disabled={loading}
              className="btn-secondary"
            >
              Test Wrong Credentials
            </button>
            <button
              onClick={testAllCredentials}
              disabled={loading}
              className="btn"
            >
              Test All Credentials
            </button>
          </div>
        </div>

        <div className="card">
          <h2 className="text-xl text-gold-luxurious mb-4">Test Results</h2>
          <pre className="text-white-off bg-gray-anthracite p-4 rounded text-sm overflow-auto max-h-96 whitespace-pre-wrap">
            {result || 'No tests run yet'}
          </pre>
          {loading && (
            <div className="mt-4 text-gold-luxurious">Testing...</div>
          )}
        </div>
      </div>
    </div>
  );
}
