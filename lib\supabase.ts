import { createClient } from '@supabase/supabase-js';

// Create Supabase client
// For development, we'll use a mock URL if environment variables are not set
let supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
let supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Check if the URL is valid, if not use a fallback
try {
  // Test if the URL is valid by creating a URL object
  if (supabaseUrl) {
    new URL(supabaseUrl);
  } else {
    // If empty or invalid, use a fallback
    supabaseUrl = 'https://example.supabase.co';
    supabaseAnonKey = 'mock-key-for-development';
    console.log('Using mock Supabase URL and key for development in supabase.ts');
  }
} catch (error) {
  // If URL is invalid, use a fallback
  console.error('Invalid Supabase URL in supabase.ts, using fallback for development');
  supabaseUrl = 'https://example.supabase.co';
  supabaseAnonKey = 'mock-key-for-development';
}

// Create the Supabase client
export const supabase = createClient(supabaseUrl, supabaseAnonKey);

/**
 * Get a member by ID
 * @param id The member ID
 * @returns The member data or null
 */
export async function getMemberById(id: string) {
  const { data, error } = await supabase
    .from('members')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching member:', error);
    return null;
  }

  return data;
}

/**
 * Get all members referred by a specific member
 * @param memberId The ID of the referring member
 * @returns An array of members
 */
export async function getReferredMembers(memberId: string) {
  const { data, error } = await supabase
    .from('members')
    .select('*')
    .eq('referrer_id', memberId);

  if (error) {
    console.error('Error fetching referred members:', error);
    return [];
  }

  return data || [];
}

/**
 * Get all NCM keys generated by a member
 * @param memberId The ID of the member
 * @returns An array of NCM keys
 */
export async function getMemberNCMKeys(memberId: string) {
  const { data, error } = await supabase
    .from('ncm_keys')
    .select('*')
    .eq('generated_by_member_id', memberId);

  if (error) {
    console.error('Error fetching NCM keys:', error);
    return [];
  }

  return data || [];
}

/**
 * Create a new member
 * @param memberData The member data
 * @returns The created member or null
 */
export async function createMember(memberData: any) {
  const { data, error } = await supabase
    .from('members')
    .insert([memberData])
    .select()
    .single();

  if (error) {
    console.error('Error creating member:', error);
    return null;
  }

  return data;
}

/**
 * Create new NCM keys
 * @param keysData Array of NCM key data
 * @returns The created keys or empty array
 */
export async function createNCMKeys(keysData: any[]) {
  const { data, error } = await supabase
    .from('ncm_keys')
    .insert(keysData)
    .select();

  if (error) {
    console.error('Error creating NCM keys:', error);
    return [];
  }

  return data || [];
}

/**
 * Update an NCM key's status
 * @param keyId The ID of the key
 * @param used Whether the key is used
 * @param assignedToMemberId The ID of the member who used the key
 * @returns The updated key or null
 */
export async function updateNCMKeyStatus(keyId: string, used: boolean, assignedToMemberId?: string) {
  const updateData: any = { used };

  if (assignedToMemberId) {
    updateData.assigned_to_member_id = assignedToMemberId;
    updateData.used_at = new Date().toISOString();
  }

  const { data, error } = await supabase
    .from('ncm_keys')
    .update(updateData)
    .eq('id', keyId)
    .select()
    .single();

  if (error) {
    console.error('Error updating NCM key:', error);
    return null;
  }

  return data;
}

/**
 * Get all products
 * @param availableOnly Whether to only return available products
 * @returns An array of products
 */
export async function getProducts(availableOnly = true) {
  let query = supabase.from('products').select('*');

  if (availableOnly) {
    query = query.eq('available', true);
  }

  const { data, error } = await query;

  if (error) {
    console.error('Error fetching products:', error);
    return [];
  }

  return data || [];
}

/**
 * Get a product by ID
 * @param id The product ID
 * @returns The product data or null
 */
export async function getProductById(id: string) {
  const { data, error } = await supabase
    .from('products')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    console.error('Error fetching product:', error);
    return null;
  }

  return data;
}

/**
 * Create a new Bitcoin payment
 * @param paymentData The payment data
 * @returns The created payment or null
 */
export async function createBitcoinPayment(paymentData: any) {
  const { data, error } = await supabase
    .from('bitcoin_payments')
    .insert([paymentData])
    .select()
    .single();

  if (error) {
    console.error('Error creating payment:', error);
    return null;
  }

  return data;
}

/**
 * Update a Bitcoin payment's status
 * @param paymentId The ID of the payment
 * @param status The new status
 * @returns The updated payment or null
 */
export async function updateBitcoinPaymentStatus(paymentId: string, status: 'pending' | 'confirmed' | 'failed') {
  const updateData: any = {
    status,
    updated_at: new Date().toISOString()
  };

  if (status === 'confirmed') {
    updateData.confirmed_at = new Date().toISOString();
  }

  const { data, error } = await supabase
    .from('bitcoin_payments')
    .update(updateData)
    .eq('id', paymentId)
    .select()
    .single();

  if (error) {
    console.error('Error updating payment:', error);
    return null;
  }

  return data;
}
