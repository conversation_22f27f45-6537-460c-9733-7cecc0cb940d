# Guide de Configuration de Supabase pour IAFUL

Ce guide vous aidera à configurer Supabase pour l'application IAFUL.

## Prérequis

1. Un compte Supabase (gratuit) : [https://supabase.com/](https://supabase.com/)
2. Node.js et npm installés sur votre machine

## Étapes de Configuration

### 1. Créer un Projet Supabase

1. Connectez-vous à votre compte Supabase
2. Cliquez sur "New Project"
3. Donnez un nom à votre projet (ex: "iaful")
4. Choisissez une région proche de vos utilisateurs
5. Définissez un mot de passe pour la base de données (conservez-le, vous en aurez besoin plus tard)
6. Clique<PERSON> sur "Create new project"

### 2. Obtenir les Informations de Connexion

Une fois votre projet créé, vous aurez besoin des informations suivantes :

1. URL de l'API : Dans le tableau de bord Supabase, allez dans "Settings" > "API" > "URL"
2. Clé Anon : Dans le tableau de bord Supabase, allez dans "Settings" > "API" > "anon public"

### 3. Configurer les Variables d'Environnement

1. Dans le dossier du projet IAFUL, créez un fichier `.env.local` (ou modifiez-le s'il existe déjà)
2. Ajoutez les variables suivantes :

```
NEXT_PUBLIC_SUPABASE_URL=votre-url-supabase
NEXT_PUBLIC_SUPABASE_ANON_KEY=votre-clé-anon
```

Remplacez `votre-url-supabase` et `votre-clé-anon` par les valeurs obtenues à l'étape précédente.

### 4. Installer les Dépendances

```bash
npm install
```

### 5. Exécuter le Script de Configuration

```bash
npm run setup-db
```

Ce script va :
- Créer les tables nécessaires dans la base de données
- Configurer les politiques de sécurité (RLS)
- Créer un utilisateur administrateur
- Générer les premiers codes NCM
- Ajouter des produits d'exemple

Suivez les instructions à l'écran pour configurer l'utilisateur administrateur.

### 6. Configurer l'Authentification

1. Dans le tableau de bord Supabase, allez dans "Authentication" > "Providers"
2. Assurez-vous que "Email" est activé
3. Vous pouvez désactiver "Phone" si vous ne l'utilisez pas
4. Dans "Site URL", entrez l'URL de votre application (ex: http://localhost:3000 pour le développement)
5. Dans "Redirect URLs", ajoutez les URLs suivantes :
   - http://localhost:3000/auth/callback (pour le développement)
   - https://votre-domaine.com/auth/callback (pour la production)

### 7. Configurer le Stockage (Optionnel)

Si vous souhaitez permettre le téléchargement d'images pour les produits :

1. Dans le tableau de bord Supabase, allez dans "Storage" > "Policies"
2. Créez un nouveau bucket nommé "products"
3. Configurez les politiques d'accès selon vos besoins

### 8. Lancer l'Application

```bash
npm run dev
```

Votre application IAFUL devrait maintenant être connectée à Supabase et prête à être utilisée !

## Structure de la Base de Données

La base de données contient les tables suivantes :

- `members` : Informations sur les membres et liens hiérarchiques
- `ncm_keys` : Codes NCM, propriété et statut
- `products` : Catalogue de produits exclusifs
- `bitcoin_payments` : Suivi des paiements
- `admin_settings` : Paramètres configurables de la plateforme
- `coupons` : Coupons de réduction

## Dépannage

### Problèmes d'Authentification

Si vous rencontrez des problèmes d'authentification :

1. Vérifiez que les variables d'environnement sont correctement configurées
2. Assurez-vous que les URLs de redirection sont correctement configurées
3. Vérifiez les journaux d'authentification dans le tableau de bord Supabase

### Problèmes de Base de Données

Si le script de configuration échoue :

1. Vérifiez que vous avez les droits d'accès à la base de données
2. Vérifiez que les variables d'environnement sont correctement configurées
3. Essayez d'exécuter les requêtes SQL manuellement dans l'éditeur SQL de Supabase

### Autres Problèmes

Pour tout autre problème, consultez la documentation de Supabase :
[https://supabase.com/docs](https://supabase.com/docs)
