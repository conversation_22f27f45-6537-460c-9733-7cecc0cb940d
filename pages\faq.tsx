import Head from 'next/head';
import { useState } from 'react';
import Layout from '../components/Layout';

interface FAQItem {
  id: number;
  question: string;
  answer: string;
}

const faqData: FAQItem[] = [
  {
    id: 1,
    question: "Quels sont les délais de livraison ?",
    answer: "Toute commande passée avant 15h est expédiée le jour même. Les commandes passées après 15h seront envoyées le lendemain. Les délais de livraison varient entre 24h et 48h selon votre lieu de résidence."
  },
  {
    id: 2,
    question: "Quels sont les frais de livraison ?",
    answer: "La livraison est gratuite pour toutes les commandes."
  },
  {
    id: 3,
    question: "Puis-je suivre ma commande ?",
    answer: "Oui. Dès l'expédition de votre commande, sur demande vous recevrez un numéro de suivi, vous permettant de suivre votre colis en temps réel."
  },
  {
    id: 4,
    question: "<PERSON>rez-vous partout ?",
    answer: "Nous livrons partout en France."
  },
  {
    id: 5,
    question: "Que faire si mon colis est en retard ou endommagé ?",
    answer: "En cas de problème, contactez notre service client par e-mail. Nous traiterons votre demande dans les heures qui suivent."
  },
  {
    id: 6,
    question: "Puis-je modifier ou annuler ma commande après validation ?",
    answer: "Une fois votre commande validée, elle est préparée rapidement. Si vous souhaitez la modifier ou l'annuler, contactez-nous dans les 30 minutes suivant votre commande."
  },
  {
    id: 7,
    question: "Proposez-vous des livraisons automatiques ?",
    answer: "Oui. Vous pourrez prochainement souscrire à 3 types d'abonnements Gold, Platine et Diamond pour recevoir vos produits automatiquement chaque mois, ou par simple clic sans avoir à repasser commande avec des privilèges pour chaque offre."
  },
  {
    id: 8,
    question: "Comment fonctionne le système de parrainage ?",
    answer: "En tant que membre, vous pouvez parrainer d'autres personnes et bénéficier de bons d'achat pour chaque commande de vos invités. Consultez la rubrique \"Parrainage\" pour en savoir plus."
  },
  {
    id: 9,
    question: "Mes commandes sont-elles anonymes ?",
    answer: "Oui, absolument. Toutes les commandes passées sur notre site sont 100 % anonymes. Aucune information personnelle n'est visible par aucun tiers, et aucune base de données n'est hébergée en Europe, pour des raisons légales et sécuritaires. Tout est sécurisé et crypté avec des protocoles de sécurité à la pointe de ce qui se fait en termes de cybersécurité et de protection de données. Nous ne partageons ni ne revendons aucune donnée. Vos informations sont utilisées uniquement pour assurer la livraison et sont strictement protégées par des protocoles de sécurité à la pointe de ce qui se fait. Votre historique de commande est effacé, et parce que votre sécurité et la nôtre et votre tranquillité et la nôtre n'ont pas de prix."
  }
];

export default function FAQ() {
  const [openItems, setOpenItems] = useState<number[]>([]);

  const toggleItem = (id: number) => {
    setOpenItems(prev => 
      prev.includes(id) 
        ? prev.filter(item => item !== id)
        : [...prev, id]
    );
  };

  return (
    <div className="min-h-screen bg-black-deep">
      <Head>
        <title>FAQ - Questions Fréquentes | IAFUL</title>
        <meta name="description" content="Trouvez les réponses à toutes vos questions sur IAFUL" />
      </Head>

      <Layout title="FAQ - Questions Fréquentes | IAFUL">
        <div className="container mx-auto px-4 py-12">
          {/* Header Section */}
          <div className="text-center mb-16">
            <h1 className="text-5xl font-playfair font-bold text-gold-luxurious mb-6">
              Questions Fréquentes
            </h1>
            <div className="w-24 h-1 bg-gradient-to-r from-gold-luxurious to-gold-warm mx-auto mb-6"></div>
            <p className="text-xl text-white-off/80 max-w-3xl mx-auto leading-relaxed">
              Trouvez rapidement les réponses à vos questions les plus courantes. 
              Notre équipe est également disponible pour vous accompagner.
            </p>
          </div>

          {/* FAQ Items */}
          <div className="max-w-4xl mx-auto">
            <div className="space-y-4">
              {faqData.map((item) => (
                <div 
                  key={item.id}
                  className="bg-gradient-to-br from-gray-anthracite/50 to-black-deep/80 backdrop-blur-sm border border-gold-luxurious/20 rounded-xl overflow-hidden hover:border-gold-luxurious/40 transition-all duration-300"
                >
                  <button
                    onClick={() => toggleItem(item.id)}
                    className="w-full px-8 py-6 text-left flex items-center justify-between hover:bg-gold-luxurious/5 transition-colors duration-300"
                  >
                    <h3 className="text-lg font-semibold text-gold-luxurious pr-4">
                      {item.question}
                    </h3>
                    <div className={`transform transition-transform duration-300 ${openItems.includes(item.id) ? 'rotate-180' : ''}`}>
                      <svg className="w-6 h-6 text-gold-luxurious" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                      </svg>
                    </div>
                  </button>
                  
                  <div className={`overflow-hidden transition-all duration-300 ${openItems.includes(item.id) ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'}`}>
                    <div className="px-8 pb-6">
                      <div className="w-full h-px bg-gradient-to-r from-transparent via-gold-luxurious/30 to-transparent mb-4"></div>
                      <p className="text-white-off/90 leading-relaxed text-base">
                        {item.answer}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Contact Section */}
          <div className="mt-16 text-center">
            <div className="inline-block p-8 rounded-2xl bg-gradient-to-br from-gray-anthracite/30 to-black-deep/50 backdrop-blur-sm border border-gold-luxurious/20">
              <h3 className="text-2xl font-playfair text-gold-luxurious mb-4">
                Vous ne trouvez pas votre réponse ?
              </h3>
              <p className="text-white-off/80 mb-6 max-w-md">
                Notre équipe est là pour vous aider. Contactez-nous et nous vous répondrons rapidement.
              </p>
              <button className="bg-gradient-to-r from-gold-luxurious to-gold-warm text-black-deep px-8 py-3 rounded-full font-semibold hover:shadow-lg hover:shadow-gold-luxurious/25 transition-all duration-300 hover:transform hover:scale-105">
                Nous Contacter
              </button>
            </div>
          </div>
        </div>
      </Layout>
    </div>
  );
}
