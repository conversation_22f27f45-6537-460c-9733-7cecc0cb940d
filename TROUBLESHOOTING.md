# Guide de Dépannage pour IAFUL

Ce guide vous aidera à résoudre les problèmes courants que vous pourriez rencontrer lors de l'exécution de l'application IAFUL.

## Problèmes de Démarrage

### Erreur "Invalid URL" lors du démarrage

**Symptôme :** Vous voyez une erreur comme celle-ci :
```
Server Error
TypeError: Invalid URL
```

**Solutions :**

1. **Utiliser le script de démarrage avec mock :**
   ```
   start-dev-with-mock.bat
   ```
   Ce script créera automatiquement un fichier `.env.local` avec des valeurs par défaut.

2. **Créer manuellement le fichier `.env.local` :**
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://example.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=mock-key-for-development
   ```

3. **Vérifier le format de l'URL Supabase :**
   Assurez-vous que l'URL Supabase commence par `https://` et ne contient pas d'espaces.

### Erreur "Module not found" lors du démarrage

**Symptôme :** Vous voyez une erreur indiquant qu'un module n'a pas été trouvé.

**Solution :**
```
npm install
```
Cette commande installera toutes les dépendances nécessaires.

## Problèmes d'Authentification

### Impossible de se connecter avec les identifiants de test

**Symptôme :** Vous ne pouvez pas vous connecter avec <NAME_EMAIL> / admin123.

**Solutions :**

1. **Utiliser le script de test de connexion :**
   ```
   test-login.bat
   ```
   Ce script ouvrira une page HTML simple qui vous permettra de tester les identifiants sans avoir à démarrer le serveur Next.js.

2. **Vérifier que vous utilisez le mode mock :**
   Assurez-vous que votre fichier `.env.local` contient les valeurs suivantes :
   ```
   NEXT_PUBLIC_SUPABASE_URL=https://example.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=mock-key-for-development
   ```

3. **Vérifier la console du navigateur :**
   Ouvrez les outils de développement du navigateur (F12) et vérifiez s'il y a des erreurs dans la console.

4. **Effacer le stockage local :**
   - Ouvrez les outils de développement du navigateur (F12)
   - Allez dans l'onglet "Application" ou "Stockage"
   - Effacez le "Local Storage" pour votre domaine

5. **Redémarrer l'application :**
   - Arrêtez le serveur de développement (Ctrl+C)
   - Exécutez le script `start-dev-with-mock.bat` pour redémarrer l'application avec les paramètres corrects

## Problèmes d'Affichage

### Les styles ne s'affichent pas correctement

**Symptôme :** L'application s'affiche sans styles ou avec des styles incorrects.

**Solutions :**

1. **Vérifier que Tailwind est correctement installé :**
   ```
   npm install tailwindcss postcss autoprefixer
   ```

2. **Vérifier les fichiers de style :**
   Assurez-vous que le fichier `styles/globals.css` existe et est correctement importé dans `_app.tsx`.

3. **Vérifier la console du navigateur :**
   Ouvrez les outils de développement du navigateur (F12) et vérifiez s'il y a des erreurs dans la console.

### La visualisation de l'arbre des membres ne s'affiche pas

**Symptôme :** La visualisation de l'arbre des membres est vide ou ne s'affiche pas.

**Solutions :**

1. **Vérifier que React Flow est correctement installé :**
   ```
   npm install reactflow
   ```

2. **Vérifier la console du navigateur :**
   Ouvrez les outils de développement du navigateur (F12) et vérifiez s'il y a des erreurs dans la console.

## Problèmes de Base de Données

### Erreur lors de l'exécution du script de configuration de la base de données

**Symptôme :** Le script `setup-db` échoue avec une erreur.

**Solutions :**

1. **Vérifier les informations de connexion Supabase :**
   Assurez-vous que les variables d'environnement `NEXT_PUBLIC_SUPABASE_URL` et `NEXT_PUBLIC_SUPABASE_ANON_KEY` sont correctement définies dans `.env.local`.

2. **Vérifier les droits d'accès :**
   Assurez-vous que vous avez les droits d'accès nécessaires pour créer des tables dans votre base de données Supabase.

3. **Exécuter les requêtes SQL manuellement :**
   Vous pouvez copier les requêtes SQL du fichier `lib/supabase-schema.sql` et les exécuter manuellement dans l'éditeur SQL de Supabase.

## Autres Problèmes

### L'application est lente ou ne répond pas

**Solutions :**

1. **Vérifier votre connexion Internet :**
   Assurez-vous que votre connexion Internet est stable.

2. **Vérifier l'utilisation des ressources :**
   Ouvrez le gestionnaire des tâches et vérifiez si votre ordinateur a suffisamment de ressources disponibles.

3. **Redémarrer le serveur de développement :**
   Arrêtez le serveur de développement (Ctrl+C) et redémarrez-le.

### Problèmes avec les images

**Symptôme :** Les images ne s'affichent pas correctement.

**Solutions :**

1. **Vérifier que les images existent :**
   Assurez-vous que les images référencées existent dans le dossier `public`.

2. **Vérifier les chemins d'accès :**
   Assurez-vous que les chemins d'accès aux images sont corrects.

3. **Utiliser le composant Image de Next.js :**
   Utilisez le composant `Image` de Next.js pour optimiser le chargement des images.

## Contacter le Support

Si vous rencontrez un problème qui n'est pas couvert par ce guide, vous pouvez :

1. Ouvrir une issue sur le dépôt GitHub
2. Contacter l'équipe de développement à l'adresse <EMAIL>

## Ressources Utiles

- [Documentation de Next.js](https://nextjs.org/docs)
- [Documentation de Supabase](https://supabase.com/docs)
- [Documentation de React Flow](https://reactflow.dev/docs/)
