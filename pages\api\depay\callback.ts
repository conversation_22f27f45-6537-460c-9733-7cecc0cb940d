import { NextApiRequest, NextApiResponse } from 'next';
import { verify } from '@depay/js-verify-signature';
import { supabase } from '../../../lib/supabase';

// DePay payment callback handler
export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify the request signature from DePay
    const signature = req.headers['x-signature'] as string;
    const publicKey = process.env.DEPAY_PUBLIC_KEY;

    if (!publicKey) {
      console.error('DEPAY_PUBLIC_KEY not configured');
      return res.status(500).json({ error: 'Payment system configuration error' });
    }

    // Verify request authenticity
    const verified = await verify({
      signature,
      data: JSON.stringify(req.body),
      publicKey,
    });

    if (!verified) {
      console.error('Invalid DePay signature');
      return res.status(401).json({ error: 'Invalid signature' });
    }

    // Extract payment data
    const {
      blockchain,
      transaction,
      sender,
      receiver,
      token,
      amount,
      payload,
      after_block,
      commitment,
      confirmations,
      created_at,
      confirmed_at
    } = req.body;

    console.log('DePay payment received:', {
      blockchain,
      transaction,
      amount,
      token,
      payload
    });

    // Extract order information from payload or URL
    let orderId = null;
    if (payload && payload.order_id) {
      orderId = payload.order_id;
    }

    if (!orderId) {
      console.error('No order ID found in payment callback');
      return res.status(400).json({ error: 'Order ID not found' });
    }

    // Fetch order details
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .select(`
        *,
        member:members(*),
        product:products(*)
      `)
      .eq('id', orderId)
      .single();

    if (orderError || !order) {
      console.error('Order not found:', orderId);
      return res.status(404).json({ error: 'Order not found' });
    }

    // Check if payment already processed
    if (order.status === 'paid' || order.status === 'fulfilled') {
      console.log('Payment already processed for order:', orderId);
      return res.status(200).json({ 
        message: 'Payment already processed',
        forward_to: `${process.env.NEXT_PUBLIC_BASE_URL}/payment/success?order=${orderId}`
      });
    }

    // Update order with payment details
    const { error: updateError } = await supabase
      .from('orders')
      .update({
        status: 'paid',
        transaction_hash: transaction,
        blockchain: blockchain,
        token_address: token,
        amount_crypto: amount,
        paid_at: confirmed_at || new Date().toISOString(),
        payment_data: {
          sender,
          receiver,
          after_block,
          commitment,
          confirmations,
          created_at,
          confirmed_at
        }
      })
      .eq('id', orderId);

    if (updateError) {
      console.error('Error updating order:', updateError);
      return res.status(500).json({ error: 'Failed to update order' });
    }

    // Log payment event
    await supabase
      .from('payment_logs')
      .insert({
        order_id: orderId,
        event_type: 'succeeded',
        transaction_data: req.body,
        created_at: new Date().toISOString()
      });

    // Mark discount as used if applicable
    if (order.discount_applied) {
      await supabase
        .from('coupons')
        .update({
          used: true,
          used_at: new Date().toISOString(),
          used_by_order_id: orderId
        })
        .eq('id', order.discount_applied);
    }

    // Process order fulfillment
    await processOrderFulfillment(order);

    // Send success response with redirect
    return res.status(200).json({
      message: 'Payment processed successfully',
      forward_to: `${process.env.NEXT_PUBLIC_BASE_URL}/payment/success?order=${orderId}`
    });

  } catch (error) {
    console.error('DePay callback error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}

// Process order fulfillment
async function processOrderFulfillment(order: any) {
  try {
    // Update order status to fulfilled
    await supabase
      .from('orders')
      .update({
        status: 'fulfilled',
        fulfilled_at: new Date().toISOString()
      })
      .eq('id', order.id);

    // Grant product access to member
    if (order.member && order.product) {
      await supabase
        .from('member_product_access')
        .insert({
          member_id: order.member.id,
          product_id: order.product.id,
          order_id: order.id,
          granted_at: new Date().toISOString(),
          expires_at: null // Permanent access for IAFUL products
        });
    }

    // Send confirmation email (if email service is configured)
    if (order.member?.email) {
      await sendOrderConfirmationEmail(order);
    }

    console.log('Order fulfilled successfully:', order.id);

  } catch (error) {
    console.error('Error fulfilling order:', error);
    
    // Log fulfillment error
    await supabase
      .from('payment_logs')
      .insert({
        order_id: order.id,
        event_type: 'fulfillment_error',
        transaction_data: { error: error.message },
        created_at: new Date().toISOString()
      });
  }
}

// Send order confirmation email
async function sendOrderConfirmationEmail(order: any) {
  try {
    // This would integrate with your email service (SendGrid, Mailgun, etc.)
    // For now, just log the email that would be sent
    console.log('Order confirmation email would be sent to:', order.member.email);
    console.log('Order details:', {
      orderId: order.id,
      productName: order.product.name,
      amount: order.amount_eur,
      transactionHash: order.transaction_hash
    });

    // TODO: Implement actual email sending
    // await emailService.send({
    //   to: order.member.email,
    //   template: 'order-confirmation',
    //   data: {
    //     memberName: order.member.name,
    //     productName: order.product.name,
    //     amount: order.amount_eur,
    //     orderId: order.id,
    //     transactionHash: order.transaction_hash
    //   }
    // });

  } catch (error) {
    console.error('Error sending confirmation email:', error);
  }
}

// Disable body parsing to get raw body for signature verification
export const config = {
  api: {
    bodyParser: {
      sizeLimit: '1mb',
    },
  },
}
