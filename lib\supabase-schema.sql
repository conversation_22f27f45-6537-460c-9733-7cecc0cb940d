-- IAFUL Database Schema for Supabase

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Members table
CREATE TABLE members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    auth_id UUID UNIQUE NOT NULL,
    name VA<PERSON>HA<PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    level INTEGER NOT NULL CHECK (level >= 0 AND level <= 6),
    referrer_id UUID REFERENCES members(id),
    ncm_key_used VARCHAR(255) NOT NULL,
    is_admin BOOLEAN DEFAULT FALSE,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- NCM Keys table
CREATE TABLE ncm_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(255) UNIQUE NOT NULL,
    level INTEGER NOT NULL CHECK (level >= 1 AND level <= 6),
    generated_by_member_id UUID REFERENCES members(id),
    assigned_to_member_id UUID REFERENCES members(id),
    used BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    used_at TIMESTAMP WITH TIME ZONE
);

-- Products table
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    image_url VARCHAR(255),
    price_eur DECIMAL(10, 2) NOT NULL,
    price_btc DECIMAL(18, 8) NOT NULL,
    available BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Bitcoin Payments table
CREATE TABLE bitcoin_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    member_id UUID NOT NULL REFERENCES members(id),
    product_id UUID NOT NULL REFERENCES products(id),
    btc_address VARCHAR(255) NOT NULL,
    amount_btc DECIMAL(18, 8) NOT NULL,
    status VARCHAR(50) NOT NULL CHECK (status IN ('pending', 'confirmed', 'failed')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    confirmed_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Coupons table
CREATE TABLE coupons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL,
    discount_pct INTEGER NOT NULL CHECK (discount_pct > 0 AND discount_pct <= 100),
    assigned_to_member_id UUID REFERENCES members(id),
    used BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    used_at TIMESTAMP WITH TIME ZONE
);

-- Admin Settings table
CREATE TABLE admin_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    setting_name VARCHAR(100) UNIQUE NOT NULL,
    value TEXT NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_members_referrer_id ON members(referrer_id);
CREATE INDEX idx_members_level ON members(level);
CREATE INDEX idx_ncm_keys_generated_by ON ncm_keys(generated_by_member_id);
CREATE INDEX idx_ncm_keys_assigned_to ON ncm_keys(assigned_to_member_id);
CREATE INDEX idx_ncm_keys_level ON ncm_keys(level);
CREATE INDEX idx_ncm_keys_used ON ncm_keys(used);
CREATE INDEX idx_bitcoin_payments_member_id ON bitcoin_payments(member_id);
CREATE INDEX idx_bitcoin_payments_status ON bitcoin_payments(status);
CREATE INDEX idx_coupons_assigned_to ON coupons(assigned_to_member_id);

-- Create a function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update the updated_at column
CREATE TRIGGER update_members_updated_at
BEFORE UPDATE ON members
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_products_updated_at
BEFORE UPDATE ON products
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_bitcoin_payments_updated_at
BEFORE UPDATE ON bitcoin_payments
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_admin_settings_updated_at
BEFORE UPDATE ON admin_settings
FOR EACH ROW
EXECUTE FUNCTION update_updated_at_column();

-- Insert default admin settings
INSERT INTO admin_settings (setting_name, value) VALUES
('bitcoin_payment_enabled', 'true'),
('initial_ncm_limit', '10'),
('whatsapp_admin_number', '+1234567890'),
('admin_email', '<EMAIL>'),
('registration_enabled', 'true');

-- Create Row Level Security (RLS) policies

-- Enable RLS on all tables
ALTER TABLE members ENABLE ROW LEVEL SECURITY;
ALTER TABLE ncm_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE bitcoin_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE coupons ENABLE ROW LEVEL SECURITY;
ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;

-- Create policies for members table
CREATE POLICY "Admins can do anything with members"
ON members FOR ALL
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM members
        WHERE members.auth_id = auth.uid() AND members.is_admin = true
    )
);

CREATE POLICY "Members can read their own data and their referrals"
ON members FOR SELECT
TO authenticated
USING (
    auth.uid() = members.auth_id OR
    members.referrer_id IN (
        SELECT id FROM members WHERE auth_id = auth.uid()
    )
);

-- Create policies for ncm_keys table
CREATE POLICY "Admins can do anything with NCM keys"
ON ncm_keys FOR ALL
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM members
        WHERE members.auth_id = auth.uid() AND members.is_admin = true
    )
);

CREATE POLICY "Members can read their own NCM keys"
ON ncm_keys FOR SELECT
TO authenticated
USING (
    generated_by_member_id IN (
        SELECT id FROM members WHERE auth_id = auth.uid()
    ) OR
    assigned_to_member_id IN (
        SELECT id FROM members WHERE auth_id = auth.uid()
    )
);

CREATE POLICY "Members can create NCM keys"
ON ncm_keys FOR INSERT
TO authenticated
WITH CHECK (
    generated_by_member_id IN (
        SELECT id FROM members WHERE auth_id = auth.uid()
    )
);

-- Create policies for products table
CREATE POLICY "Anyone can read available products"
ON products FOR SELECT
TO authenticated
USING (available = true);

CREATE POLICY "Admins can do anything with products"
ON products FOR ALL
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM members
        WHERE members.auth_id = auth.uid() AND members.is_admin = true
    )
);

-- Create policies for bitcoin_payments table
CREATE POLICY "Admins can do anything with payments"
ON bitcoin_payments FOR ALL
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM members
        WHERE members.auth_id = auth.uid() AND members.is_admin = true
    )
);

CREATE POLICY "Members can read and create their own payments"
ON bitcoin_payments FOR SELECT
TO authenticated
USING (
    member_id IN (
        SELECT id FROM members WHERE auth_id = auth.uid()
    )
);

CREATE POLICY "Members can create their own payments"
ON bitcoin_payments FOR INSERT
TO authenticated
WITH CHECK (
    member_id IN (
        SELECT id FROM members WHERE auth_id = auth.uid()
    )
);

-- Create policies for coupons table
CREATE POLICY "Admins can do anything with coupons"
ON coupons FOR ALL
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM members
        WHERE members.auth_id = auth.uid() AND members.is_admin = true
    )
);

CREATE POLICY "Members can read their own coupons"
ON coupons FOR SELECT
TO authenticated
USING (
    assigned_to_member_id IN (
        SELECT id FROM members WHERE auth_id = auth.uid()
    )
);

-- Create policies for admin_settings table
CREATE POLICY "Admins can do anything with settings"
ON admin_settings FOR ALL
TO authenticated
USING (
    EXISTS (
        SELECT 1 FROM members
        WHERE members.auth_id = auth.uid() AND members.is_admin = true
    )
);

CREATE POLICY "Anyone can read certain settings"
ON admin_settings FOR SELECT
TO authenticated
USING (
    setting_name IN ('registration_enabled', 'bitcoin_payment_enabled')
);

-- Create a function to check if a member can generate more NCM keys
CREATE OR REPLACE FUNCTION can_generate_ncm_keys(member_id UUID, count INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
    member_level INTEGER;
    max_keys INTEGER;
    current_keys INTEGER;
BEGIN
    -- Get the member's level
    SELECT level INTO member_level FROM members WHERE id = member_id;
    
    -- Determine max keys based on level
    CASE member_level
        WHEN 1 THEN max_keys := 10;
        WHEN 2 THEN max_keys := 8;
        WHEN 3 THEN max_keys := 6;
        WHEN 4 THEN max_keys := 4;
        WHEN 5 THEN max_keys := 2;
        ELSE max_keys := 0;
    END CASE;
    
    -- Count current keys
    SELECT COUNT(*) INTO current_keys FROM ncm_keys WHERE generated_by_member_id = member_id;
    
    -- Check if member can generate more keys
    RETURN (current_keys + count) <= max_keys;
END;
$$ LANGUAGE plpgsql;
