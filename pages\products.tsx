import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import { AuthContext, supabase } from './_app';
import { mockDB } from '../lib/mockAuth';
import BackgroundImage from '../components/BackgroundImage';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

export default function Products() {
  const router = useRouter();
  const { user, session, useMockAuth } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [products, setProducts] = useState<any[]>([]);

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      router.push('/login');
      return;
    }

    // Fetch products
    const fetchProducts = async () => {
      try {
        setLoading(true);

        if (useMockAuth) {
          // Use mock data for development
          const productsData = await mockDB.getProducts(true);
          setProducts(productsData || []);
        } else {
          // Use Supabase for production
          const { data, error } = await supabase
            .from('products')
            .select('*')
            .eq('available', true);

          if (error) {
            throw new Error('Error fetching products');
          }

          setProducts(data || []);
        }
      } catch (error) {
        console.error('Error fetching products:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProducts();
  }, [user, router]);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/');
  };

  // Placeholder products for demonstration
  const placeholderProducts = [
    {
      id: 1,
      name: 'Royal OG',
      description: 'Une variété premium avec des notes terreuses et boisées.',
      image_url: '/royal_og.jpg',
      price_eur: 120,
      price_btc: 0.0032,
    },
    {
      id: 2,
      name: 'Lemon Octane',
      description: 'Saveurs d agrumes intenses avec une puissance remarquable.',
      image_url: '/lemon_octane.jpg',
      price_eur: 140,
      price_btc: 0.0037,
    },
    {
      id: 3,
      name: 'Bubble Kush',
      description: 'Arômes sucrés et fruités avec des effets relaxants profonds.',
      image_url: '/bubble_kush.jpg',
      price_eur: 130,
      price_btc: 0.0035,
    },
    {
      id: 4,
      name: 'Easy Weed',
      description: 'Parfaite pour les débutants, avec un profil équilibré.',
      image_url: '/fleur-easy_weed.jpg',
      price_eur: 110,
      price_btc: 0.0029,
    },
  ];

  if (loading) {
    return (
      <div className="min-h-screen bg-black-deep relative">
        {/* Background image */}
        <BackgroundImage opacity={80} imagePath="/background.jpg" />

        <Head>
          <title>Chargement... | IAFUL</title>
          <meta name="description" content="IAFUL - Le Club Privé Réservé à une Élite Sélectionnée" />
          <link rel="icon" href="/favicon.ico" />
        </Head>

        <Navbar />

        <main className="pt-24 pb-20 relative z-10">
          <div className="flex items-center justify-center h-64">
            <div className="text-gold-luxurious text-xl">Chargement...</div>
          </div>
        </main>

        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black-deep relative">
      {/* Background image */}
      <BackgroundImage opacity={80} imagePath="/background.jpg" />

      <Head>
        <title>Produits Exclusifs | IAFUL</title>
        <meta name="description" content="IAFUL - Le Club Privé Réservé à une Élite Sélectionnée" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Navbar />

      <main className="pt-24 pb-20 relative z-10">
        <div className="container mx-auto px-4">
          {/* Hero Section */}
          <div className="relative rounded-xl overflow-hidden mb-12 bg-gray-anthracite/60 backdrop-blur-sm shadow-xl">
            <div className="absolute inset-0 bg-gradient-to-r from-black-deep to-transparent z-10"></div>
            <div className="relative h-64 md:h-80">
              <Image
                src="/premium_products_banner.jpg"
                alt="Produits Premium"
                fill
                priority
                className="object-cover opacity-60"
              />
            </div>
            <div className="absolute inset-0 flex flex-col justify-center z-20 p-8 md:p-12">
              <h1 className="text-4xl md:text-5xl font-playfair font-bold text-gold-luxurious mb-4 tracking-wide max-w-xl">
                Produits Exclusifs
              </h1>
              <p className="text-white-off text-lg md:text-xl max-w-xl mb-6">
                Découvrez notre sélection de produits premium, disponibles uniquement pour nos membres.
              </p>
              <div className="flex space-x-4">
                <button className="bg-gold-luxurious/20 border border-gold-luxurious text-gold-luxurious px-6 py-2 rounded-button hover:bg-gold-luxurious/30 transition-colors">
                  Nouveautés
                </button>
                <button className="bg-gray-anthracite/60 text-white-off px-6 py-2 rounded-button hover:bg-gray-anthracite/80 transition-colors">
                  Voir tout
                </button>
              </div>
            </div>
          </div>

          {/* Filter and Sort Section */}
          <div className="flex flex-col md:flex-row justify-between items-center mb-8 bg-gray-anthracite/60 backdrop-blur-sm p-4 rounded-lg">
            <div className="flex items-center space-x-4 mb-4 md:mb-0">
              <span className="text-white-off/70">Filtrer par:</span>
              <div className="relative">
                <select className="bg-black-deep border border-gold-luxurious/30 text-white-off rounded-button py-2 px-4 appearance-none focus:outline-none focus:border-gold-luxurious">
                  <option>Tous les produits</option>
                  <option>Premium</option>
                  <option>Standard</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gold-luxurious">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd"></path>
                  </svg>
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <span className="text-white-off/70">Trier par:</span>
              <div className="relative">
                <select className="bg-black-deep border border-gold-luxurious/30 text-white-off rounded-button py-2 px-4 appearance-none focus:outline-none focus:border-gold-luxurious">
                  <option>Recommandés</option>
                  <option>Prix: croissant</option>
                  <option>Prix: décroissant</option>
                </select>
                <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gold-luxurious">
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd"></path>
                  </svg>
                </div>
              </div>
            </div>
          </div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 max-w-7xl mx-auto">
            {placeholderProducts.map((product) => (
              <Link href={`/products/${product.id}`} key={product.id} className="group">
                <div className="bg-gray-anthracite/60 backdrop-blur-sm rounded-lg overflow-hidden shadow-lg hover:shadow-gold-luxurious/20 transition-all duration-300 hover:translate-y-[-5px]">
                  <div className="relative h-64 overflow-hidden">
                    <div className="absolute inset-0 bg-black-deep/30 z-10 group-hover:opacity-0 transition-opacity"></div>
                    <Image
                      src={product.image_url}
                      alt={product.name}
                      fill
                      className="object-cover transition-transform duration-500 group-hover:scale-110"
                    />
                    <div className="absolute top-4 right-4 z-20">
                      <span className="bg-gold-luxurious text-black-deep text-xs font-bold px-3 py-1 rounded-full">
                        Premium
                      </span>
                    </div>
                    <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black-deep to-transparent h-20 z-10"></div>
                  </div>

                  <div className="p-5">
                    <div className="flex items-center mb-2">
                      <div className="flex text-gold-luxurious">
                        {[...Array(5)].map((_, i) => (
                          <svg key={i} className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                          </svg>
                        ))}
                      </div>
                      <span className="text-white-off/50 ml-2 text-xs">(8)</span>
                    </div>

                    <h3 className="text-xl font-playfair text-gold-luxurious group-hover:text-gold-luxurious/80 transition-colors mb-2">
                      {product.name}
                    </h3>

                    <p className="text-white-off/80 mb-4 text-sm line-clamp-2">
                      {product.description}
                    </p>

                    <div className="flex justify-between items-center">
                      <div>
                        <p className="text-gold-luxurious font-bold text-lg">{product.price_eur} €</p>
                        <p className="text-white-off/50 text-xs">{product.price_btc} BTC</p>
                      </div>

                      <button className="bg-gold-luxurious/10 hover:bg-gold-luxurious/20 border border-gold-luxurious/30 text-gold-luxurious p-2 rounded-full transition-colors">
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </Link>
            ))}
          </div>

          {/* Pagination */}
          <div className="flex justify-center mt-12">
            <nav className="flex items-center space-x-2">
              <button className="px-3 py-2 rounded-md bg-gray-anthracite/60 text-white-off/70 hover:text-gold-luxurious transition-colors">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clipRule="evenodd"></path>
                </svg>
              </button>
              <button className="px-4 py-2 rounded-md bg-gold-luxurious text-black-deep font-medium">1</button>
              <button className="px-4 py-2 rounded-md bg-gray-anthracite/60 text-white-off hover:text-gold-luxurious transition-colors">2</button>
              <button className="px-4 py-2 rounded-md bg-gray-anthracite/60 text-white-off hover:text-gold-luxurious transition-colors">3</button>
              <button className="px-3 py-2 rounded-md bg-gray-anthracite/60 text-white-off/70 hover:text-gold-luxurious transition-colors">
                <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                  <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
                </svg>
              </button>
            </nav>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
