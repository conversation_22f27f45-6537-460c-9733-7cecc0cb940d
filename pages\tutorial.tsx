import Head from 'next/head';
import { useState } from 'react';
import Layout from '../components/Layout';

interface TutorialStep {
  id: number;
  title: string;
  content: string;
  icon: string;
}

const tutorialSteps: TutorialStep[] = [
  {
    id: 1,
    title: "Télécharger un portefeuille (Wallet)",
    content: "Pour commencer, vous avez besoin d'un portefeuille numérique pour stocker vos cryptomonnaies. Je vous recommande MetaMask, l'un des portefeuilles les plus populaires et faciles à utiliser.\n\nSur ordinateur :\n• Rendez-vous sur le site officiel de MetaMask : metamask.io\n• Cliquez sur \"Télécharger\" et choisissez l'extension pour votre navigateur (Chrome, Firefox, Brave, etc.)\n• Suivez les instructions pour installer l'extension et créer un nouveau portefeuille\n• Notez soigneusement votre phrase de récupération (12 mots) et gardez-la en sécurité\n\nSur téléphone :\n• Téléchargez l'application MetaMask sur l'App Store (iOS) ou Google Play Store (Android)\n• Ouvrez l'application et créez un nouveau portefeuille\n• Sauvegardez votre phrase de récupération\n\nMetaMask est compatible avec de nombreuses blockchains (Ethereum, Binance Smart Chain, etc.) et peut être connecté à des plateformes d'échange ou à des applications décentralisées (dApps).",
    icon: "💳"
  },
  {
    id: 2,
    title: "Acheter de la crypto avec KYC",
    content: "Si vous êtes prêt à fournir une pièce d'identité, voici comment acheter de la crypto facilement :\n\n• Inscrivez-vous sur une plateforme d'échange centralisée comme Binance, Coinbase ou Kraken\n• Complétez la vérification KYC en fournissant une pièce d'identité et une preuve d'adresse\n• Ajoutez des fonds via carte bancaire, virement ou autre méthode de paiement\n• Achetez la crypto de votre choix (Bitcoin, Ethereum, etc.) et transférez-la vers votre portefeuille MetaMask",
    icon: "🆔"
  },
  {
    id: 3,
    title: "Acheter de la crypto sans KYC",
    content: "Si vous préférez éviter la vérification d'identité, voici une méthode simple :\n\n• Utilisez une plateforme décentralisée comme Changelly, SimpleSwap ou LocalCryptos\n• Connectez votre portefeuille MetaMask à la plateforme\n• Choisissez la crypto que vous souhaitez acheter et la méthode de paiement (carte bancaire, autre crypto, etc.)\n• Suivez les instructions pour finaliser l'achat. La crypto sera directement envoyée à votre portefeuille",
    icon: "🔒"
  },
  {
    id: 4,
    title: "Sécuriser vos cryptos",
    content: "Une fois vos cryptos dans votre portefeuille, assurez-vous de les sécuriser :\n\n• Ne partagez jamais votre phrase de récupération avec qui que ce soit\n• Activez l'authentification à deux facteurs (2FA) si disponible\n• Gardez vos clés privées en sécurité et hors ligne\n• Utilisez des mots de passe forts et uniques\n\nEt voilà ! Vous êtes prêt à acheter et à gérer vos cryptomonnaies en toute sécurité et simplicité.",
    icon: "🛡️"
  }
];

export default function Tutorial() {
  const [activeStep, setActiveStep] = useState<number>(1);

  return (
    <div className="min-h-screen bg-black-deep">
      <Head>
        <title>Guide Crypto | IAFUL</title>
        <meta name="description" content="Apprenez à utiliser les cryptomonnaies avec notre guide complet" />
      </Head>

      <Layout title="Guide Crypto | IAFUL">
        <div className="container mx-auto px-4 py-12">
          {/* Header Section */}
          <div className="text-center mb-16">
            <h1 className="text-5xl font-playfair font-bold text-gold-luxurious mb-6">
              Guide Cryptomonnaies
            </h1>
            <div className="w-24 h-1 bg-gradient-to-r from-gold-luxurious to-gold-warm mx-auto mb-6"></div>
            <p className="text-xl text-white-off/80 max-w-3xl mx-auto leading-relaxed">
              Découvrez comment acheter et utiliser les cryptomonnaies en toute sécurité. 
              Un guide simple et complet pour débuter.
            </p>
          </div>

          <div className="max-w-6xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
              {/* Step Navigation */}
              <div className="lg:col-span-1">
                <div className="sticky top-8">
                  <h3 className="text-xl font-playfair text-gold-luxurious mb-6">Étapes</h3>
                  <div className="space-y-3">
                    {tutorialSteps.map((step) => (
                      <button
                        key={step.id}
                        onClick={() => setActiveStep(step.id)}
                        className={`w-full text-left p-4 rounded-lg transition-all duration-300 ${
                          activeStep === step.id
                            ? 'bg-gold-luxurious/20 border border-gold-luxurious text-gold-luxurious'
                            : 'bg-gray-anthracite/30 border border-gray-anthracite/50 text-white-off/70 hover:bg-gray-anthracite/50'
                        }`}
                      >
                        <div className="flex items-center space-x-3">
                          <span className="text-2xl">{step.icon}</span>
                          <div>
                            <div className="text-sm font-medium">Étape {step.id}</div>
                            <div className="text-xs opacity-80">{step.title}</div>
                          </div>
                        </div>
                      </button>
                    ))}
                  </div>
                </div>
              </div>

              {/* Step Content */}
              <div className="lg:col-span-3">
                {tutorialSteps.map((step) => (
                  <div
                    key={step.id}
                    className={`transition-all duration-500 ${
                      activeStep === step.id ? 'block' : 'hidden'
                    }`}
                  >
                    <div className="bg-gradient-to-br from-gray-anthracite/50 to-black-deep/80 backdrop-blur-sm border border-gold-luxurious/20 rounded-2xl p-8">
                      <div className="flex items-center space-x-4 mb-6">
                        <div className="text-4xl">{step.icon}</div>
                        <div>
                          <div className="text-sm text-gold-luxurious/70 font-medium">Étape {step.id}</div>
                          <h2 className="text-2xl font-playfair font-bold text-gold-luxurious">
                            {step.title}
                          </h2>
                        </div>
                      </div>

                      <div className="prose prose-invert max-w-none">
                        <div className="text-white-off/90 leading-relaxed whitespace-pre-line">
                          {step.content}
                        </div>
                      </div>

                      {/* Navigation Buttons */}
                      <div className="flex justify-between items-center mt-8 pt-6 border-t border-gold-luxurious/20">
                        <button
                          onClick={() => setActiveStep(Math.max(1, step.id - 1))}
                          disabled={step.id === 1}
                          className={`flex items-center space-x-2 px-6 py-3 rounded-full transition-all duration-300 ${
                            step.id === 1
                              ? 'opacity-50 cursor-not-allowed bg-gray-anthracite/30 text-white-off/50'
                              : 'bg-gray-anthracite/60 text-white-off hover:bg-gray-anthracite/80'
                          }`}
                        >
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 19l-7-7 7-7" />
                          </svg>
                          <span>Précédent</span>
                        </button>

                        <div className="flex space-x-2">
                          {tutorialSteps.map((_, index) => (
                            <div
                              key={index}
                              className={`w-3 h-3 rounded-full transition-all duration-300 ${
                                index + 1 === activeStep
                                  ? 'bg-gold-luxurious'
                                  : index + 1 < activeStep
                                  ? 'bg-gold-luxurious/50'
                                  : 'bg-gray-anthracite/50'
                              }`}
                            />
                          ))}
                        </div>

                        <button
                          onClick={() => setActiveStep(Math.min(tutorialSteps.length, step.id + 1))}
                          disabled={step.id === tutorialSteps.length}
                          className={`flex items-center space-x-2 px-6 py-3 rounded-full transition-all duration-300 ${
                            step.id === tutorialSteps.length
                              ? 'opacity-50 cursor-not-allowed bg-gray-anthracite/30 text-white-off/50'
                              : 'bg-gradient-to-r from-gold-luxurious to-gold-warm text-black-deep hover:shadow-lg hover:shadow-gold-luxurious/25'
                          }`}
                        >
                          <span>Suivant</span>
                          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Bottom CTA */}
          <div className="mt-16 text-center">
            <div className="inline-block p-8 rounded-2xl bg-gradient-to-br from-gray-anthracite/30 to-black-deep/50 backdrop-blur-sm border border-gold-luxurious/20">
              <h3 className="text-2xl font-playfair text-gold-luxurious mb-4">
                Prêt à commencer ?
              </h3>
              <p className="text-white-off/80 mb-6 max-w-md">
                Maintenant que vous maîtrisez les bases, découvrez notre collection exclusive.
              </p>
              <button className="bg-gradient-to-r from-gold-luxurious to-gold-warm text-black-deep px-8 py-3 rounded-full font-semibold hover:shadow-lg hover:shadow-gold-luxurious/25 transition-all duration-300 hover:transform hover:scale-105">
                Voir les Produits
              </button>
            </div>
          </div>
        </div>
      </Layout>
    </div>
  );
}
