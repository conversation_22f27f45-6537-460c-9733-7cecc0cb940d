import Head from 'next/head';
import Link from 'next/link';
import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';
import { AuthContext, supabase } from './_app';
import BackgroundImage from '../components/BackgroundImage';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

// Dynamically import the MemberTree component to avoid SSR issues with ReactFlow
const MemberTree = dynamic(() => import('../components/MemberTree'), {
  ssr: false,
  loading: () => (
    <div className="bg-gray-anthracite border border-gold-luxurious/30 rounded-md p-4 h-64 flex items-center justify-center">
      <p className="text-white-off/70">Chargement de la visualisation...</p>
    </div>
  ),
});



export default function Profile() {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [memberData, setMemberData] = useState<any>(null);
  const [ncmKeys, setNcmKeys] = useState<any[]>([]);
  const [referrals, setReferrals] = useState<any[]>([]);
  const [coupons, setCoupons] = useState<any[]>([]);

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      router.push('/login');
      return;
    }

    // Fetch member data
    const fetchMemberData = async () => {
      try {
        setLoading(true);

        // Fetch member profile
        const { data: memberData, error: memberError } = await supabase
          .from('members')
          .select('*')
          .eq('auth_id', user.id)
          .single();

        if (memberError) {
          throw new Error('Error fetching member data');
        }

        setMemberData(memberData);

        // Fetch NCM keys generated by this member
        const { data: keysData, error: keysError } = await supabase
          .from('ncm_keys')
          .select('*')
          .eq('generated_by_member_id', memberData.id);

        if (keysError) {
          throw new Error('Error fetching NCM keys');
        }

        setNcmKeys(keysData || []);

        // Fetch coupons for this member
        const { data: couponsData, error: couponsError } = await supabase
          .from('coupons')
          .select('*')
          .eq('assigned_to_member_id', memberData.id);

        if (couponsError) {
          console.error('Error fetching coupons:', couponsError);
        } else {
          setCoupons(couponsData || []);
        }

        // Fetch referrals only for non-exclusive members
        if (!memberData.is_exclusive) {
          const { data: referralsData, error: referralsError } = await supabase
            .from('members')
            .select('*')
            .eq('referrer_id', memberData.id);

          if (referralsError) {
            throw new Error('Error fetching referrals');
          }

          setReferrals(referralsData || []);
        }
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMemberData();
  }, [user, router]);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black-deep relative">
        {/* Background image */}
        <BackgroundImage opacity={80} imagePath="/background.jpg" />

        <Head>
          <title>Chargement... | IAFUL</title>
          <meta name="description" content="IAFUL - Le Club Privé Réservé à une Élite Sélectionnée" />
          <link rel="icon" href="/favicon.ico" />
        </Head>

        <Navbar />

        <main className="pt-24 pb-20 relative z-10">
          <div className="flex items-center justify-center h-64">
            <div className="text-gold-luxurious text-xl">Chargement...</div>
          </div>
        </main>

        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black-deep relative">
      {/* Background image */}
      <BackgroundImage opacity={80} imagePath="/background.jpg" />

      <Head>
        <title>Mon Profil | IAFUL</title>
        <meta name="description" content="IAFUL - Le Club Privé Réservé à une Élite Sélectionnée" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Navbar />

      <main className="pt-24 pb-20 relative z-10">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl font-playfair font-bold text-gold-luxurious mb-8">
              Bienvenue, {memberData?.name || 'Membre'}
              {memberData?.is_exclusive && (
                <span className="ml-3 px-3 py-1 bg-gradient-to-r from-gold-luxurious to-gold-warm text-black-deep text-sm font-medium rounded-full">
                  Membre Exclusif
                </span>
              )}
            </h1>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="card">
                <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Informations</h2>
                <div className="space-y-2">
                  <p className="text-white-off">
                    <span className="text-gold-luxurious/70">Nom:</span> {memberData?.name}
                  </p>
                  <p className="text-white-off">
                    <span className="text-gold-luxurious/70">Email:</span> {user?.email}
                  </p>
                  <p className="text-white-off">
                    <span className="text-gold-luxurious/70">Type:</span> {memberData?.is_exclusive ? 'Membre Exclusif' : `Niveau ${memberData?.level || 'N/A'}`}
                  </p>
                  <p className="text-white-off">
                    <span className="text-gold-luxurious/70">NCM utilisé:</span> {memberData?.ncm_key_used || 'N/A'}
                  </p>
                  {memberData?.is_exclusive && (
                    <p className="text-white-off">
                      <span className="text-gold-luxurious/70">Statut:</span>
                      <span className="ml-2 px-2 py-1 bg-gold-luxurious/20 text-gold-luxurious text-xs rounded">
                        Accès Premium
                      </span>
                    </p>
                  )}
                </div>
              </div>

              <div className="card md:col-span-2">
                {memberData?.is_exclusive ? (
                  <>
                    <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Mes Coupons & Réductions</h2>
                    {coupons.length > 0 ? (
                      <div className="space-y-4">
                        {coupons.map((coupon) => (
                          <div key={coupon.id} className="flex justify-between items-center p-4 bg-gradient-to-r from-gold-luxurious/10 to-gold-warm/10 border border-gold-luxurious/30 rounded-md">
                            <div>
                              <p className="text-white-off font-medium text-lg">{coupon.code}</p>
                              <p className="text-gold-luxurious font-bold">
                                -{coupon.discount_pct}% de réduction
                              </p>
                              <p className="text-sm text-white-off/70">
                                {coupon.used ? 'Utilisé' : 'Disponible'}
                                {coupon.expires_at && (
                                  <span className="ml-2">
                                    • Expire le {new Date(coupon.expires_at).toLocaleDateString()}
                                  </span>
                                )}
                              </p>
                            </div>
                            <div className="flex flex-col space-y-2">
                              <button
                                className="text-sm text-gold-luxurious hover:underline"
                                onClick={() => {
                                  navigator.clipboard.writeText(coupon.code);
                                  alert('Code coupon copié dans le presse-papier');
                                }}
                              >
                                Copier le code
                              </button>
                              {!coupon.used && (
                                <span className="px-2 py-1 bg-green-900/30 text-green-400 text-xs rounded-full text-center">
                                  Actif
                                </span>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <div className="text-gold-luxurious/50 text-4xl mb-4">🎫</div>
                        <p className="text-white-off">Aucun coupon disponible pour le moment.</p>
                        <p className="text-white-off/70 text-sm mt-2">
                          Les coupons et réductions vous seront attribués par l'administration.
                        </p>
                      </div>
                    )}
                  </>
                ) : (
                  <>
                    <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Mes NCM</h2>
                    {ncmKeys.length > 0 ? (
                      <div className="space-y-4">
                        {ncmKeys.map((key) => (
                          <div key={key.id} className="flex justify-between items-center p-3 bg-black-deep rounded-md">
                            <div>
                              <p className="text-white-off font-medium">{key.key}</p>
                              <p className="text-sm text-white-off/70">
                                {key.used ? 'Utilisé' : 'Disponible'}
                              </p>
                            </div>
                            <button
                              className="text-sm text-gold-luxurious hover:underline"
                              onClick={() => {
                                navigator.clipboard.writeText(key.key);
                                alert('NCM copié dans le presse-papier');
                              }}
                            >
                              Copier
                            </button>
                          </div>
                        ))}
                      </div>
                    ) : (
                      <p className="text-white-off">Aucun NCM généré pour le moment.</p>
                    )}
                  </>
                )}
              </div>
            </div>

            {!memberData?.is_exclusive && (
              <div className="card mb-12">
                <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Mon Réseau</h2>
                <p className="text-white-off mb-6">
                  Visualisez votre réseau de membres parrainés et leur hiérarchie.
                </p>
                {referrals.length > 0 ? (
                  <MemberTree
                    rootMember={memberData}
                    members={[...referrals, memberData]}
                    compact={true}
                  />
                ) : (
                  <div className="bg-gray-anthracite border border-gold-luxurious/30 rounded-md p-4 h-64 flex items-center justify-center">
                    <p className="text-white-off/70">
                      La visualisation du réseau sera disponible lorsque vous aurez des membres parrainés.
                    </p>
                  </div>
                )}
              </div>
            )}

            <div className="card">
              <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Actions</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Link href="/products" className="btn text-center py-3">
                  Voir les produits
                </Link>
                {!memberData?.is_exclusive && (
                  <Link href="/generate-ncm" className="btn text-center py-3">
                    Générer des NCM
                  </Link>
                )}
                {memberData?.is_exclusive && (
                  <button
                    className="btn text-center py-3 opacity-50 cursor-not-allowed"
                    disabled
                    title="Les membres exclusifs n'ont pas accès à la génération de NCM"
                  >
                    Accès Premium Exclusif
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
