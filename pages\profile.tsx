import Head from 'next/head';
import Link from 'next/link';
import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';
import { AuthContext, supabase } from './_app';
import BackgroundImage from '../components/BackgroundImage';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

// Dynamically import the MemberTree component to avoid SSR issues with ReactFlow
const MemberTree = dynamic(() => import('../components/MemberTree'), {
  ssr: false,
  loading: () => (
    <div className="bg-gray-anthracite border border-gold-luxurious/30 rounded-md p-4 h-64 flex items-center justify-center">
      <p className="text-white-off/70">Chargement de la visualisation...</p>
    </div>
  ),
});



export default function Profile() {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [memberData, setMemberData] = useState<any>(null);
  const [ncmKeys, setNcmKeys] = useState<any[]>([]);
  const [referrals, setReferrals] = useState<any[]>([]);

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      router.push('/login');
      return;
    }

    // Fetch member data
    const fetchMemberData = async () => {
      try {
        setLoading(true);

        // Fetch member profile
        const { data: memberData, error: memberError } = await supabase
          .from('members')
          .select('*')
          .eq('auth_id', user.id)
          .single();

        if (memberError) {
          throw new Error('Error fetching member data');
        }

        setMemberData(memberData);

        // Fetch NCM keys generated by this member
        const { data: keysData, error: keysError } = await supabase
          .from('ncm_keys')
          .select('*')
          .eq('generated_by_member_id', memberData.id);

        if (keysError) {
          throw new Error('Error fetching NCM keys');
        }

        setNcmKeys(keysData || []);

        // Fetch referrals (members referred by this user)
        const { data: referralsData, error: referralsError } = await supabase
          .from('members')
          .select('*')
          .eq('referrer_id', memberData.id);

        if (referralsError) {
          throw new Error('Error fetching referrals');
        }

        setReferrals(referralsData || []);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMemberData();
  }, [user, router]);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-black-deep relative">
        {/* Background image */}
        <BackgroundImage opacity={80} imagePath="/background.jpg" />

        <Head>
          <title>Chargement... | IAFUL</title>
          <meta name="description" content="IAFUL - Le Club Privé Réservé à une Élite Sélectionnée" />
          <link rel="icon" href="/favicon.ico" />
        </Head>

        <Navbar />

        <main className="pt-24 pb-20 relative z-10">
          <div className="flex items-center justify-center h-64">
            <div className="text-gold-luxurious text-xl">Chargement...</div>
          </div>
        </main>

        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black-deep relative">
      {/* Background image */}
      <BackgroundImage opacity={80} imagePath="/background.jpg" />

      <Head>
        <title>Mon Profil | IAFUL</title>
        <meta name="description" content="IAFUL - Le Club Privé Réservé à une Élite Sélectionnée" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Navbar />

      <main className="pt-24 pb-20 relative z-10">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <h1 className="text-3xl font-playfair font-bold text-gold-luxurious mb-8">
              Bienvenue, {memberData?.name || 'Membre'}
            </h1>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
              <div className="card">
                <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Informations</h2>
                <div className="space-y-2">
                  <p className="text-white-off">
                    <span className="text-gold-luxurious/70">Nom:</span> {memberData?.name}
                  </p>
                  <p className="text-white-off">
                    <span className="text-gold-luxurious/70">Email:</span> {user?.email}
                  </p>
                  <p className="text-white-off">
                    <span className="text-gold-luxurious/70">Niveau:</span> {memberData?.level || 'N/A'}
                  </p>
                  <p className="text-white-off">
                    <span className="text-gold-luxurious/70">NCM utilisé:</span> {memberData?.ncm_key_used || 'N/A'}
                  </p>
                </div>
              </div>

              <div className="card md:col-span-2">
                <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Mes NCM</h2>
                {ncmKeys.length > 0 ? (
                  <div className="space-y-4">
                    {ncmKeys.map((key) => (
                      <div key={key.id} className="flex justify-between items-center p-3 bg-black-deep rounded-md">
                        <div>
                          <p className="text-white-off font-medium">{key.key}</p>
                          <p className="text-sm text-white-off/70">
                            {key.used ? 'Utilisé' : 'Disponible'}
                          </p>
                        </div>
                        <button
                          className="text-sm text-gold-luxurious hover:underline"
                          onClick={() => {
                            navigator.clipboard.writeText(key.key);
                            alert('NCM copié dans le presse-papier');
                          }}
                        >
                          Copier
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-white-off">Aucun NCM généré pour le moment.</p>
                )}
              </div>
            </div>

            <div className="card mb-12">
              <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Mon Réseau</h2>
              <p className="text-white-off mb-6">
                Visualisez votre réseau de membres parrainés et leur hiérarchie.
              </p>
              {referrals.length > 0 ? (
                <MemberTree
                  rootMember={memberData}
                  members={[...referrals, memberData]}
                  compact={true}
                />
              ) : (
                <div className="bg-gray-anthracite border border-gold-luxurious/30 rounded-md p-4 h-64 flex items-center justify-center">
                  <p className="text-white-off/70">
                    La visualisation du réseau sera disponible lorsque vous aurez des membres parrainés.
                  </p>
                </div>
              )}
            </div>

            <div className="card">
              <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Actions</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <Link href="/products" className="btn text-center py-3">
                  Voir les produits
                </Link>
                <Link href="/generate-ncm" className="btn text-center py-3">
                  Générer des NCM
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
