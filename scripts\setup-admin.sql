-- IAFUL Admin Setup Script
-- This script sets up the initial admin user for the IAFUL application

-- Note: This script should be run after the user has been created in Supabase Auth
-- with email: <EMAIL> and password: 152dh#HF47ev@2

-- Step 1: First, create the user in Supabase Auth Dashboard or via API
-- Email: <EMAIL>
-- Password: 152dh#HF47ev@2

-- Step 2: Get the auth_id from the auth.users table and replace 'YOUR_AUTH_ID_HERE' below
-- You can find this in Supabase Dashboard > Authentication > Users

-- Step 3: Insert the admin member record
INSERT INTO members (
    auth_id,
    name,
    email,
    level,
    referrer_id,
    ncm_key_used,
    is_admin,
    is_exclusive,
    active,
    created_at,
    updated_at
) VALUES (
    'YOUR_AUTH_ID_HERE', -- Replace with actual auth_id from Supabase Auth
    'Hassen Admin',
    '<EMAIL>',
    0, -- Admin level
    NULL, -- No referrer for admin
    'ADMIN-INIT-001', -- Special admin NCM key
    TRUE, -- Is admin
    FALSE, -- Not exclusive member
    TRUE, -- Active
    NOW(),
    NOW()
) ON CONFLICT (auth_id) DO UPDATE SET
    is_admin = TRUE,
    name = 'Hassen Admin',
    email = '<EMAIL>',
    updated_at = NOW();

-- Step 4: Verify the admin user was created
SELECT 
    id,
    auth_id,
    name,
    email,
    is_admin,
    created_at
FROM members 
WHERE email = '<EMAIL>';

-- Optional: Create some initial admin settings
INSERT INTO admin_settings (
    setting_key,
    setting_value,
    description,
    created_at,
    updated_at
) VALUES 
    ('site_name', 'IAFUL', 'Site name for the application', NOW(), NOW()),
    ('admin_email', '<EMAIL>', 'Primary admin email', NOW(), NOW()),
    ('max_ncm_generation', '100', 'Maximum NCM keys that can be generated at once', NOW(), NOW()),
    ('exclusive_member_limit', '1000', 'Maximum number of exclusive members', NOW(), NOW())
ON CONFLICT (setting_key) DO UPDATE SET
    setting_value = EXCLUDED.setting_value,
    updated_at = NOW();

-- Create admin_settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS admin_settings (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    setting_key VARCHAR(255) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create index for admin_settings
CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON admin_settings(setting_key);

-- Grant necessary permissions (if using RLS)
-- ALTER TABLE members ENABLE ROW LEVEL SECURITY;
-- ALTER TABLE admin_settings ENABLE ROW LEVEL SECURITY;

-- Create policy for admin access to members table
-- CREATE POLICY "Admins can manage all members" ON members
--     FOR ALL USING (
--         EXISTS (
--             SELECT 1 FROM members admin_member 
--             WHERE admin_member.auth_id = auth.uid() 
--             AND admin_member.is_admin = TRUE
--         )
--     );

-- Create policy for admin access to admin_settings table
-- CREATE POLICY "Admins can manage settings" ON admin_settings
--     FOR ALL USING (
--         EXISTS (
--             SELECT 1 FROM members admin_member 
--             WHERE admin_member.auth_id = auth.uid() 
--             AND admin_member.is_admin = TRUE
--         )
--     );

-- Display final confirmation
SELECT 
    'Admin setup completed successfully!' as message,
    COUNT(*) as admin_count
FROM members 
WHERE is_admin = TRUE;
