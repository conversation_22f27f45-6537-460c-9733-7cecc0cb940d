import '../styles/globals.css';
import type { AppProps } from 'next/app';
import { createContext, useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { mockDB } from '../lib/mockAuth';

// Create Supabase client with environment variables
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || '';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '';

// Determine if we should use mock authentication
const useMockAuth = !supabaseUrl || !supabaseAnonKey ||
  supabaseUrl === 'https://example.supabase.co' ||
  supabaseAnonKey === 'mock-key-for-development';

if (useMockAuth) {
  console.log('Using mock authentication for development');
} else {
  console.log('Using Supabase authentication');
}

// Create the Supabase client (even for mock mode, to avoid errors)
export const supabase = createClient(
  supabaseUrl || 'https://example.supabase.co',
  supabaseAnonKey || 'mock-key-for-development'
);

// Create auth context
export const AuthContext = createContext<{
  user: any;
  session: any;
  useMockAuth: boolean;
  signIn: (email: string, password: string) => Promise<any>;
  signOut: () => Promise<void>;
}>({
  user: null,
  session: null,
  useMockAuth: false,
  signIn: async () => {},
  signOut: async () => {},
});

function MyApp({ Component, pageProps }: AppProps) {
  const [user, setUser] = useState<any>(null);
  const [session, setSession] = useState<any>(null);

  // Mock authentication functions
  const mockSignIn = async (email: string, password: string) => {
    console.log('Context mockSignIn called with:', { email, password });
    try {
      const { data, error } = await mockDB.signInWithPassword(email, password);
      console.log('mockDB.signInWithPassword result:', { data, error });

      if (error) {
        throw new Error(error.message);
      }
      if (data.user) {
        const newSession = {
          user: data.user
        };
        console.log('Setting session and user:', { newSession, user: data.user });
        setSession(newSession);
        setUser(data.user);
      }
      return { data, error };
    } catch (err) {
      console.error('Error in mockSignIn:', err);
      throw err;
    }
  };

  const mockSignOut = async () => {
    await mockDB.signOut();
    setSession(null);
    setUser(null);
  };

  // Supabase authentication functions
  const supabaseSignIn = async (email: string, password: string) => {
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });
    return { data, error };
  };

  const supabaseSignOut = async () => {
    await supabase.auth.signOut();
  };

  // Choose the appropriate sign in/out functions
  const signIn = useMockAuth ? mockSignIn : supabaseSignIn;
  const signOut = useMockAuth ? mockSignOut : supabaseSignOut;

  useEffect(() => {
    if (useMockAuth) {
      // Use mock authentication
      const getInitialMockSession = async () => {
        const { data: { session } } = await mockDB.getSession();
        setSession(session);
        setUser(session?.user ?? null);
      };
      getInitialMockSession();
    } else {
      // Use Supabase authentication
      const getInitialSession = async () => {
        const { data: { session } } = await supabase.auth.getSession();
        setSession(session);
        setUser(session?.user ?? null);
      };

      getInitialSession();

      // Listen for auth changes
      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        (_event, session) => {
          setSession(session);
          setUser(session?.user ?? null);
        }
      );

      return () => {
        subscription.unsubscribe();
      };
    }
  }, []);

  return (
    <AuthContext.Provider value={{ user, session, useMockAuth, signIn, signOut }}>
      <Component {...pageProps} />
    </AuthContext.Provider>
  );
}

export default MyApp;
