import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import Layout from '../../components/Layout';
import { AuthContext, supabase } from '../_app';

export default function AdminSettings() {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [adminData, setAdminData] = useState<any>(null);
  const [passwordForm, setPasswordForm] = useState({
    currentPassword: '',
    newEmail: '',
    newPassword: '',
    confirmPassword: '',
  });
  const [profileForm, setProfileForm] = useState({
    name: '',
    email: '',
  });
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);

  useEffect(() => {
    const checkAdminAndLoadData = async () => {
      if (!user) {
        router.push('/login');
        return;
      }

      try {
        // Check if user is admin
        const { data: memberData, error: memberError } = await supabase
          .from('members')
          .select('*')
          .eq('auth_id', user.id)
          .single();

        if (memberError || !memberData?.is_admin) {
          router.push('/profile');
          return;
        }

        setAdminData(memberData);
        setProfileForm({
          name: memberData.name || '',
          email: user.email || '',
        });
        setPasswordForm(prev => ({
          ...prev,
          newEmail: user.email || '',
        }));
      } catch (error) {
        console.error('Error checking admin status:', error);
        router.push('/profile');
      } finally {
        setLoading(false);
      }
    };

    checkAdminAndLoadData();
  }, [user, router]);

  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setMessage(null);

    try {
      // Update member profile
      const { error: memberError } = await supabase
        .from('members')
        .update({
          name: profileForm.name,
          email: profileForm.email,
          updated_at: new Date().toISOString(),
        })
        .eq('auth_id', user.id);

      if (memberError) {
        throw new Error('Erreur lors de la mise à jour du profil');
      }

      // Update auth email if changed
      if (profileForm.email !== user.email) {
        const { error: authError } = await supabase.auth.updateUser({
          email: profileForm.email,
        });

        if (authError) {
          throw new Error('Erreur lors de la mise à jour de l\'email');
        }
      }

      setMessage({ type: 'success', text: 'Profil mis à jour avec succès' });
      
      // Update local state
      setAdminData(prev => ({ ...prev, name: profileForm.name, email: profileForm.email }));
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message });
    } finally {
      setSaving(false);
    }
  };

  const handlePasswordUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);
    setMessage(null);

    try {
      // Validate passwords match
      if (passwordForm.newPassword !== passwordForm.confirmPassword) {
        throw new Error('Les mots de passe ne correspondent pas');
      }

      if (passwordForm.newPassword.length < 8) {
        throw new Error('Le mot de passe doit contenir au moins 8 caractères');
      }

      // Update password
      const { error: passwordError } = await supabase.auth.updateUser({
        password: passwordForm.newPassword,
      });

      if (passwordError) {
        throw new Error('Erreur lors de la mise à jour du mot de passe');
      }

      // Update email if changed
      if (passwordForm.newEmail !== user.email) {
        const { error: emailError } = await supabase.auth.updateUser({
          email: passwordForm.newEmail,
        });

        if (emailError) {
          throw new Error('Erreur lors de la mise à jour de l\'email');
        }

        // Update member email
        await supabase
          .from('members')
          .update({ email: passwordForm.newEmail })
          .eq('auth_id', user.id);
      }

      setMessage({ type: 'success', text: 'Identifiants mis à jour avec succès' });
      
      // Clear password fields
      setPasswordForm(prev => ({
        ...prev,
        currentPassword: '',
        newPassword: '',
        confirmPassword: '',
      }));
    } catch (error: any) {
      setMessage({ type: 'error', text: error.message });
    } finally {
      setSaving(false);
    }
  };

  const handlePasswordFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setPasswordForm(prev => ({ ...prev, [name]: value }));
  };

  const handleProfileFormChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setProfileForm(prev => ({ ...prev, [name]: value }));
  };

  if (loading) {
    return (
      <Layout title="Paramètres Admin | IAFUL" isAdmin>
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center h-64">
            <div className="text-gold-luxurious text-xl">Chargement...</div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Paramètres Admin | IAFUL" isAdmin>
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-playfair font-bold text-gold-luxurious mb-8">
            Paramètres Administrateur
          </h1>
          
          {message && (
            <div className={`mb-6 p-4 rounded-md ${
              message.type === 'success' 
                ? 'bg-green-900/30 border border-green-500/50 text-green-400' 
                : 'bg-red-900/30 border border-red-500/50 text-red-400'
            }`}>
              {message.text}
            </div>
          )}

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Profile Settings */}
            <div className="card">
              <h2 className="text-xl font-playfair text-gold-luxurious mb-6">
                Informations du Profil
              </h2>
              
              <form onSubmit={handleProfileUpdate} className="space-y-4">
                <div>
                  <label htmlFor="name" className="block text-white-off mb-2">
                    Nom d'affichage
                  </label>
                  <input
                    type="text"
                    id="name"
                    name="name"
                    value={profileForm.name}
                    onChange={handleProfileFormChange}
                    className="input-field w-full"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="profileEmail" className="block text-white-off mb-2">
                    Email du profil
                  </label>
                  <input
                    type="email"
                    id="profileEmail"
                    name="email"
                    value={profileForm.email}
                    onChange={handleProfileFormChange}
                    className="input-field w-full"
                    required
                  />
                </div>
                
                <button
                  type="submit"
                  disabled={saving}
                  className="btn w-full py-3"
                >
                  {saving ? 'Mise à jour...' : 'Mettre à jour le profil'}
                </button>
              </form>
            </div>

            {/* Security Settings */}
            <div className="card">
              <h2 className="text-xl font-playfair text-gold-luxurious mb-6">
                Sécurité & Connexion
              </h2>
              
              <form onSubmit={handlePasswordUpdate} className="space-y-4">
                <div>
                  <label htmlFor="newEmail" className="block text-white-off mb-2">
                    Email de connexion
                  </label>
                  <input
                    type="email"
                    id="newEmail"
                    name="newEmail"
                    value={passwordForm.newEmail}
                    onChange={handlePasswordFormChange}
                    className="input-field w-full"
                    required
                  />
                </div>
                
                <div>
                  <label htmlFor="newPassword" className="block text-white-off mb-2">
                    Nouveau mot de passe
                  </label>
                  <input
                    type="password"
                    id="newPassword"
                    name="newPassword"
                    value={passwordForm.newPassword}
                    onChange={handlePasswordFormChange}
                    className="input-field w-full"
                    minLength={8}
                    required
                  />
                  <p className="text-white-off/60 text-xs mt-1">
                    Minimum 8 caractères
                  </p>
                </div>
                
                <div>
                  <label htmlFor="confirmPassword" className="block text-white-off mb-2">
                    Confirmer le mot de passe
                  </label>
                  <input
                    type="password"
                    id="confirmPassword"
                    name="confirmPassword"
                    value={passwordForm.confirmPassword}
                    onChange={handlePasswordFormChange}
                    className="input-field w-full"
                    minLength={8}
                    required
                  />
                </div>
                
                <button
                  type="submit"
                  disabled={saving}
                  className="btn w-full py-3 bg-gradient-to-r from-gold-luxurious to-gold-warm text-black-deep"
                >
                  {saving ? 'Mise à jour...' : 'Mettre à jour les identifiants'}
                </button>
              </form>
            </div>
          </div>

          {/* Current Admin Info */}
          <div className="card mt-8">
            <h2 className="text-xl font-playfair text-gold-luxurious mb-6">
              Informations Actuelles
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-gray-anthracite p-4 rounded-md">
                <div className="text-white-off/70 text-sm">Nom</div>
                <div className="text-gold-luxurious text-lg font-medium">{adminData?.name}</div>
              </div>
              
              <div className="bg-gray-anthracite p-4 rounded-md">
                <div className="text-white-off/70 text-sm">Email</div>
                <div className="text-gold-luxurious text-lg font-medium">{user?.email}</div>
              </div>
              
              <div className="bg-gray-anthracite p-4 rounded-md">
                <div className="text-white-off/70 text-sm">Statut</div>
                <div className="text-gold-luxurious text-lg font-medium">
                  <span className="px-2 py-1 bg-gold-luxurious/20 text-gold-luxurious text-sm rounded">
                    Administrateur
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Security Notice */}
          <div className="bg-gradient-to-r from-gold-luxurious/10 to-gold-warm/10 border border-gold-luxurious/30 rounded-md p-6 mt-8">
            <h3 className="text-gold-luxurious font-medium mb-2">🔐 Sécurité</h3>
            <ul className="text-white-off/80 text-sm space-y-1">
              <li>• Utilisez un mot de passe fort avec au moins 8 caractères</li>
              <li>• Gardez vos identifiants confidentiels</li>
              <li>• Déconnectez-vous après chaque session</li>
              <li>• Vérifiez régulièrement l'activité de votre compte</li>
            </ul>
          </div>
        </div>
      </div>
    </Layout>
  );
}
