-- IAFUL DePay Integration Database Schema
-- This file contains the database schema for DePay payment integration

-- Orders table to track all payment orders
CREATE TABLE IF NOT EXISTS orders (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    member_id UUID REFERENCES members(auth_id),
    product_id UUID REFERENCES products(id),
    amount_eur DECIMAL(10,2) NOT NULL,
    amount_crypto DECIMAL(18,8),
    token_address VARCHAR(42),
    blockchain VARCHAR(20),
    transaction_hash VARCHAR(66),
    status VARCHAR(20) DEFAULT 'pending' CHECK (status IN ('pending', 'paid', 'fulfilled', 'failed', 'cancelled')),
    discount_applied UUID REFERENCES coupons(id),
    payment_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    paid_at TIMESTAMP WITH TIME ZONE,
    fulfilled_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Payment logs table for detailed tracking
CREATE TABLE IF NOT EXISTS payment_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    order_id UUID REFERENCES orders(id),
    event_type VARCHAR(20) NOT NULL CHECK (event_type IN ('created', 'attempt', 'processing', 'succeeded', 'failed', 'fulfillment_error')),
    transaction_data JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Member product access table
CREATE TABLE IF NOT EXISTS member_product_access (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    member_id UUID REFERENCES members(auth_id),
    product_id UUID REFERENCES products(id),
    order_id UUID REFERENCES orders(id),
    granted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    expires_at TIMESTAMP WITH TIME ZONE,
    active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Coupons table (if not already exists)
CREATE TABLE IF NOT EXISTS coupons (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    code VARCHAR(50) UNIQUE NOT NULL,
    discount_pct INTEGER NOT NULL CHECK (discount_pct > 0 AND discount_pct <= 100),
    assigned_to_member_id UUID REFERENCES members(auth_id),
    used BOOLEAN DEFAULT FALSE,
    used_at TIMESTAMP WITH TIME ZONE,
    used_by_order_id UUID REFERENCES orders(id),
    expires_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_orders_member_id ON orders(member_id);
CREATE INDEX IF NOT EXISTS idx_orders_status ON orders(status);
CREATE INDEX IF NOT EXISTS idx_orders_created_at ON orders(created_at);
CREATE INDEX IF NOT EXISTS idx_orders_transaction_hash ON orders(transaction_hash);

CREATE INDEX IF NOT EXISTS idx_payment_logs_order_id ON payment_logs(order_id);
CREATE INDEX IF NOT EXISTS idx_payment_logs_event_type ON payment_logs(event_type);
CREATE INDEX IF NOT EXISTS idx_payment_logs_created_at ON payment_logs(created_at);

CREATE INDEX IF NOT EXISTS idx_member_product_access_member_id ON member_product_access(member_id);
CREATE INDEX IF NOT EXISTS idx_member_product_access_product_id ON member_product_access(product_id);
CREATE INDEX IF NOT EXISTS idx_member_product_access_active ON member_product_access(active);

CREATE INDEX IF NOT EXISTS idx_coupons_code ON coupons(code);
CREATE INDEX IF NOT EXISTS idx_coupons_assigned_to_member_id ON coupons(assigned_to_member_id);
CREATE INDEX IF NOT EXISTS idx_coupons_used ON coupons(used);

-- Add updated_at trigger for orders
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_orders_updated_at 
    BEFORE UPDATE ON orders 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_coupons_updated_at 
    BEFORE UPDATE ON coupons 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Row Level Security (RLS) policies
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE payment_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE member_product_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE coupons ENABLE ROW LEVEL SECURITY;

-- Policy for members to view their own orders
CREATE POLICY "Members can view their own orders" ON orders
    FOR SELECT USING (
        member_id = auth.uid() OR 
        EXISTS (
            SELECT 1 FROM members 
            WHERE members.auth_id = auth.uid() 
            AND members.is_admin = TRUE
        )
    );

-- Policy for admins to manage all orders
CREATE POLICY "Admins can manage all orders" ON orders
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM members 
            WHERE members.auth_id = auth.uid() 
            AND members.is_admin = TRUE
        )
    );

-- Policy for members to view their own payment logs
CREATE POLICY "Members can view their own payment logs" ON payment_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders 
            WHERE orders.id = payment_logs.order_id 
            AND orders.member_id = auth.uid()
        ) OR
        EXISTS (
            SELECT 1 FROM members 
            WHERE members.auth_id = auth.uid() 
            AND members.is_admin = TRUE
        )
    );

-- Policy for members to view their own product access
CREATE POLICY "Members can view their own product access" ON member_product_access
    FOR SELECT USING (
        member_id = auth.uid() OR 
        EXISTS (
            SELECT 1 FROM members 
            WHERE members.auth_id = auth.uid() 
            AND members.is_admin = TRUE
        )
    );

-- Policy for members to view their own coupons
CREATE POLICY "Members can view their own coupons" ON coupons
    FOR SELECT USING (
        assigned_to_member_id = auth.uid() OR 
        EXISTS (
            SELECT 1 FROM members 
            WHERE members.auth_id = auth.uid() 
            AND members.is_admin = TRUE
        )
    );

-- Policy for admins to manage all coupons
CREATE POLICY "Admins can manage all coupons" ON coupons
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM members 
            WHERE members.auth_id = auth.uid() 
            AND members.is_admin = TRUE
        )
    );

-- Insert sample coupons for exclusive members (optional)
INSERT INTO coupons (code, discount_pct, assigned_to_member_id, expires_at) VALUES
    ('EXCLUSIVE10', 10, NULL, NOW() + INTERVAL '1 year'),
    ('EXCLUSIVE15', 15, NULL, NOW() + INTERVAL '1 year'),
    ('EXCLUSIVE20', 20, NULL, NOW() + INTERVAL '1 year'),
    ('VIP25', 25, NULL, NOW() + INTERVAL '1 year'),
    ('PREMIUM30', 30, NULL, NOW() + INTERVAL '1 year')
ON CONFLICT (code) DO NOTHING;

-- Create a view for order statistics
CREATE OR REPLACE VIEW order_statistics AS
SELECT 
    COUNT(*) as total_orders,
    COUNT(*) FILTER (WHERE status = 'pending') as pending_orders,
    COUNT(*) FILTER (WHERE status = 'paid') as paid_orders,
    COUNT(*) FILTER (WHERE status = 'fulfilled') as fulfilled_orders,
    COUNT(*) FILTER (WHERE status = 'failed') as failed_orders,
    SUM(amount_eur) FILTER (WHERE status IN ('paid', 'fulfilled')) as total_revenue_eur,
    AVG(amount_eur) FILTER (WHERE status IN ('paid', 'fulfilled')) as average_order_value,
    COUNT(DISTINCT member_id) FILTER (WHERE status IN ('paid', 'fulfilled')) as unique_customers,
    DATE_TRUNC('day', created_at) as order_date
FROM orders
GROUP BY DATE_TRUNC('day', created_at)
ORDER BY order_date DESC;

-- Create a view for member purchase history
CREATE OR REPLACE VIEW member_purchase_history AS
SELECT 
    m.auth_id,
    m.name as member_name,
    m.email,
    m.is_exclusive,
    COUNT(o.id) as total_orders,
    SUM(o.amount_eur) FILTER (WHERE o.status IN ('paid', 'fulfilled')) as total_spent,
    MAX(o.created_at) as last_order_date,
    COUNT(mpa.id) as products_owned
FROM members m
LEFT JOIN orders o ON m.auth_id = o.member_id
LEFT JOIN member_product_access mpa ON m.auth_id = mpa.member_id AND mpa.active = TRUE
GROUP BY m.auth_id, m.name, m.email, m.is_exclusive;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON orders TO authenticated;
GRANT SELECT, INSERT ON payment_logs TO authenticated;
GRANT SELECT, INSERT ON member_product_access TO authenticated;
GRANT SELECT, UPDATE ON coupons TO authenticated;

GRANT SELECT ON order_statistics TO authenticated;
GRANT SELECT ON member_purchase_history TO authenticated;

-- Comments for documentation
COMMENT ON TABLE orders IS 'Stores all payment orders with DePay integration';
COMMENT ON TABLE payment_logs IS 'Detailed logs of all payment events and transactions';
COMMENT ON TABLE member_product_access IS 'Tracks which products members have access to';
COMMENT ON TABLE coupons IS 'Discount coupons for exclusive members';

COMMENT ON COLUMN orders.status IS 'Order status: pending, paid, fulfilled, failed, cancelled';
COMMENT ON COLUMN orders.payment_data IS 'Raw payment data from DePay callback';
COMMENT ON COLUMN payment_logs.event_type IS 'Event type: created, attempt, processing, succeeded, failed, fulfillment_error';
COMMENT ON COLUMN coupons.discount_pct IS 'Discount percentage (1-100)';

-- Success message
SELECT 'DePay integration database schema created successfully!' as message;
