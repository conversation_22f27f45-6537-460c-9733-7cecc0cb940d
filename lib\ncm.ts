import crypto from 'crypto';

/**
 * Generate a checksum for an NCM key
 * @param key The key without the checksum
 * @returns A 4-character checksum
 */
export function generateChecksum(key: string): string {
  // Create a SHA-256 hash of the key
  const hash = crypto.createHash('sha256').update(key).digest('hex');
  
  // Take the first 4 characters of the hash as the checksum
  return hash.substring(0, 4).toUpperCase();
}

/**
 * Validate an NCM key's checksum
 * @param key The full NCM key including checksum
 * @returns Boolean indicating if the checksum is valid
 */
export function validateChecksum(key: string): boolean {
  // Extract the parts of the key
  const parts = key.split('-');
  
  if (parts.length !== 4) {
    return false;
  }
  
  const level = parts[1];
  const randomHash = parts[2];
  const providedChecksum = parts[3];
  
  // Recreate the key without the checksum
  const keyWithoutChecksum = `NCM-${level}-${randomHash}`;
  
  // Generate the checksum for comparison
  const calculatedChecksum = generateChecksum(keyWithoutChecksum);
  
  // Compare the checksums
  return providedChecksum === calculatedChecksum;
}

/**
 * Generate a new NCM key
 * @param level The level of the key (1-6)
 * @returns A complete NCM key with checksum
 */
export function generateNCMKey(level: number): string {
  if (level < 1 || level > 6) {
    throw new Error('Level must be between 1 and 6');
  }
  
  // Generate a random hash (6 characters)
  const randomHash = crypto.randomBytes(3).toString('hex').toUpperCase();
  
  // Create the key without checksum
  const keyWithoutChecksum = `NCM-${level}-${randomHash}`;
  
  // Generate the checksum
  const checksum = generateChecksum(keyWithoutChecksum);
  
  // Return the complete key
  return `${keyWithoutChecksum}-${checksum}`;
}

/**
 * Generate multiple NCM keys
 * @param level The level of the keys (1-6)
 * @param count The number of keys to generate
 * @returns An array of NCM keys
 */
export function generateMultipleNCMKeys(level: number, count: number): string[] {
  const keys: string[] = [];
  
  for (let i = 0; i < count; i++) {
    keys.push(generateNCMKey(level));
  }
  
  return keys;
}

/**
 * Get the number of NCM keys a member can generate based on their level
 * @param level The level of the member (1-5)
 * @returns The number of keys the member can generate
 */
export function getNCMKeyCountForLevel(level: number): number {
  switch (level) {
    case 1:
      return 10;
    case 2:
      return 8;
    case 3:
      return 6;
    case 4:
      return 4;
    case 5:
      return 2;
    default:
      return 0;
  }
}
