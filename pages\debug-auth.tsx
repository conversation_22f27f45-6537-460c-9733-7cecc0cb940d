import { useState, useContext } from 'react';
import { AuthContext } from './_app';
import { mockDB } from '../lib/mockAuth';

export default function DebugAuth() {
  const { user, session, useMockAuth, signIn, signOut } = useContext(AuthContext);
  const [testResult, setTestResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testMockAuth = async () => {
    setLoading(true);
    setTestResult('');
    
    try {
      console.log('Testing mock authentication...');
      
      // Test 1: Check if mock mode is enabled
      console.log('Mock mode enabled:', useMockAuth);
      setTestResult(prev => prev + `Mock mode: ${useMockAuth}\n`);
      
      // Test 2: Check available users
      const users = (mockDB as any).users;
      console.log('Available users:', users);
      setTestResult(prev => prev + `Available users: ${JSON.stringify(users, null, 2)}\n`);
      
      // Test 3: Try to sign in with admin credentials
      console.log('Attempting to sign in with admin credentials...');
      const result = await signIn('<EMAIL>', 'admin123');
      console.log('Sign in result:', result);
      setTestResult(prev => prev + `Sign in result: ${JSON.stringify(result, null, 2)}\n`);
      
      // Test 4: Check current context state
      console.log('Current user in context:', user);
      console.log('Current session in context:', session);
      setTestResult(prev => prev + `Context user: ${JSON.stringify(user, null, 2)}\n`);
      setTestResult(prev => prev + `Context session: ${JSON.stringify(session, null, 2)}\n`);
      
    } catch (error) {
      console.error('Test error:', error);
      setTestResult(prev => prev + `Error: ${error}\n`);
    } finally {
      setLoading(false);
    }
  };

  const testSignOut = async () => {
    try {
      await signOut();
      setTestResult(prev => prev + 'Signed out successfully\n');
    } catch (error) {
      setTestResult(prev => prev + `Sign out error: ${error}\n`);
    }
  };

  return (
    <div className="min-h-screen bg-black-deep p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gold-luxurious mb-8">Authentication Debug</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="card">
            <h2 className="text-xl text-gold-luxurious mb-4">Current State</h2>
            <div className="space-y-2 text-white-off">
              <p><strong>Mock Mode:</strong> {useMockAuth ? 'Yes' : 'No'}</p>
              <p><strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'None'}</p>
              <p><strong>Session:</strong> {session ? JSON.stringify(session, null, 2) : 'None'}</p>
            </div>
          </div>

          <div className="card">
            <h2 className="text-xl text-gold-luxurious mb-4">Test Actions</h2>
            <div className="space-y-4">
              <button
                onClick={testMockAuth}
                disabled={loading}
                className="btn w-full"
              >
                {loading ? 'Testing...' : 'Test Mock Authentication'}
              </button>
              
              <button
                onClick={testSignOut}
                className="btn-secondary w-full"
              >
                Test Sign Out
              </button>
            </div>
          </div>
        </div>

        <div className="card mt-8">
          <h2 className="text-xl text-gold-luxurious mb-4">Test Results</h2>
          <pre className="text-white-off bg-gray-anthracite p-4 rounded text-sm overflow-auto max-h-96">
            {testResult || 'No tests run yet'}
          </pre>
        </div>

        <div className="card mt-8">
          <h2 className="text-xl text-gold-luxurious mb-4">Test Credentials</h2>
          <div className="text-white-off space-y-2">
            <p><strong>Admin:</strong> <EMAIL> / admin123</p>
            <p><strong>Member:</strong> <EMAIL> / member123</p>
            <p><strong>Member 2:</strong> <EMAIL> / member123</p>
            <p><strong>Member 3:</strong> <EMAIL> / member123</p>
          </div>
        </div>
      </div>
    </div>
  );
}
