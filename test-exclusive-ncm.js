// Test script for exclusive NCM key generation
const { 
  generateExclusiveNCMKey, 
  generateMultipleExclusiveNCMKeys,
  validateExclusiveNC<PERSON>Key,
  getExclusiveKeyStats 
} = require('./lib/exclusiveNCM.ts');

console.log('🔐 Testing Exclusive NCM Key Generation System\n');

// Test single key generation
console.log('1. Generating single exclusive NCM key:');
const singleKey = generateExclusiveNCMKey();
console.log(`   Generated: ${singleKey}`);
console.log(`   Valid: ${validateExclusiveNCMKey(singleKey)}`);

const stats = getExclusiveKeyStats(singleKey);
console.log(`   Binary: ${stats.binaryPart}`);
console.log(`   Decimal: ${stats.decimalValue}`);
console.log(`   Checksum: ${stats.checksum}`);
console.log(`   Ones: ${stats.onesCount}, Zeros: ${stats.zerosCount}\n`);

// Test multiple key generation
console.log('2. Generating 5 exclusive NCM keys:');
const multipleKeys = generateMultipleExclusiveNCMKeys(5);
multipleKeys.forEach((key, index) => {
  const isValid = validateExclusiveNCMKey(key);
  console.log(`   ${index + 1}. ${key} - ${isValid ? '✅ Valid' : '❌ Invalid'}`);
});

console.log('\n3. Key format validation tests:');
const testKeys = [
  'EXC-101010101-A',  // Valid format
  'EXC-110011001-F',  // Valid format
  'EXC-123456789-X',  // Invalid (non-binary)
  'NCM-101010101-A',  // Invalid prefix
  'EXC-10101010-A',   // Invalid length
];

testKeys.forEach(key => {
  const isValid = validateExclusiveNCMKey(key);
  console.log(`   ${key} - ${isValid ? '✅ Valid' : '❌ Invalid'}`);
});

console.log('\n🎉 Exclusive NCM key system is working correctly!');
