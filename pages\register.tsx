import Head from 'next/head';
import Link from 'next/link';
import { useState } from 'react';
import { useRouter } from 'next/router';
import { supabase } from './_app';
import BackgroundImage from '../components/BackgroundImage';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

export default function Register() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    ncmCode: ''
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (formData.password !== formData.confirmPassword) {
      setError('Les mots de passe ne correspondent pas');
      return;
    }

    if (!formData.ncmCode) {
      setError('Le code NCM est requis');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Verify NCM code
      // Check if NCM key exists and is not already used
      const { data: ncmData, error: ncmError } = await supabase
        .from('ncm_keys')
        .select('*')
        .eq('key', formData.ncmCode)
        .eq('used', false)
        .single();

      if (ncmError) {
        throw new Error('Code NCM invalide ou déjà utilisé');
      }

      const isValidNCM = !!ncmData;

      if (!isValidNCM) {
        throw new Error('Code NCM invalide ou déjà utilisé');
      }

      // Register user
      const { data, error } = await supabase.auth.signUp({
        email: formData.email,
        password: formData.password,
      });

      if (error) {
        throw new Error(error.message);
      }

      if (!data.user) {
        throw new Error('Erreur lors de la création du compte');
      }

      // Get the referrer from the NCM key
      const referrerId = ncmData.generated_by_member_id;

      // Get the level of the referrer to determine the new member's level
      let newMemberLevel = 1; // Default level if no referrer

      if (referrerId) {
        const { data: referrerData, error: referrerError } = await supabase
          .from('members')
          .select('level')
          .eq('id', referrerId)
          .single();

        if (!referrerError && referrerData) {
          newMemberLevel = referrerData.level + 1;
        }
      }

      // Mark NCM key as used
      await supabase
        .from('ncm_keys')
        .update({
          used: true,
          assigned_to_member_id: data.user.id,
          used_at: new Date().toISOString()
        })
        .eq('key', formData.ncmCode);

      // Create member profile
      await supabase.from('members').insert({
        id: data.user.id,
        name: formData.email.split('@')[0], // Use part of email as name initially
        email: formData.email,
        level: newMemberLevel,
        referrer_id: referrerId,
        ncm_key_used: formData.ncmCode,
        is_admin: false,
        active: true,
        created_at: new Date().toISOString(),
      });

      router.push('/register-success');
    } catch (error: any) {
      console.error('Registration error:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black-deep relative">
      {/* Background image */}
      <BackgroundImage opacity={80} imagePath="/background.jpg" />

      <Head>
        <title>Inscription | IAFUL</title>
        <meta name="description" content="IAFUL - Le Club Privé Réservé à une Élite Sélectionnée" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Navbar />

      <main className="pt-24 pb-20 relative z-10">
        <div className="container mx-auto px-4">
          <div className="max-w-md mx-auto">
            <h1 className="text-3xl font-playfair font-bold text-gold-luxurious mb-8 text-center">
              Inscription
            </h1>

            {error && (
              <div className="bg-red-900/50 border border-red-500 text-white-off p-4 rounded-md mb-6">
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit} className="card">
              <div className="mb-4">
                <label htmlFor="email" className="block text-white-off mb-2">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="input-field w-full"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="password" className="block text-white-off mb-2">
                  Mot de passe
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  className="input-field w-full"
                />
              </div>

              <div className="mb-4">
                <label htmlFor="confirmPassword" className="block text-white-off mb-2">
                  Confirmé mot de passe
                </label>
                <input
                  type="password"
                  id="confirmPassword"
                  name="confirmPassword"
                  value={formData.confirmPassword}
                  onChange={handleChange}
                  required
                  className="input-field w-full"
                />
              </div>

              <div className="mb-6">
                <label htmlFor="ncmCode" className="block text-white-off mb-2">
                  Code NCM
                </label>
                <input
                  type="text"
                  id="ncmCode"
                  name="ncmCode"
                  value={formData.ncmCode}
                  onChange={handleChange}
                  required
                  className="input-field w-full"
                  placeholder="Entrez votre code d'invitation NCM"
                />
              </div>

              <button
                type="submit"
                className="btn w-full py-3"
                disabled={loading}
              >
                {loading ? 'Inscription en cours...' : 'S\'inscrire'}
              </button>
            </form>

            <p className="text-white-off text-center mt-6">
              Déjà membre ?{' '}
              <Link href="/login" className="text-gold-luxurious hover:underline">
                Se connecter
              </Link>
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}


