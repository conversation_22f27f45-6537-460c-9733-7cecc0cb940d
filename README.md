# IAFUL - Club Privé

Une application web avec une structure d'adhésion pyramidale pour la vente de produits exclusifs aux membres enregistrés.

## Structure de l'adhésion

L'application utilise un système pyramidal basé sur des codes NCM (Numéro Club de Membre) :

- Admin : Génère 10 codes NCM pour le Niveau 1
- Niveau 1 (10 membres) : Chacun génère 10 codes NCM pour le Niveau 2
- Niveau 2 (100 membres) : Chacun génère 8 codes NCM pour le Niveau 3
- Niveau 3 (800 membres) : Chacun génère 6 codes NCM pour le Niveau 4
- Niveau 4 (4800 membres) : Chacun génère 4 codes NCM pour le Niveau 5
- Niveau 5 (19200 membres) : Chacun génère 2 codes NCM pour le Niveau 6 (38400 membres)

## Fonctionnalités principales

### Zone publique
- Page d'accueil avec présentation du concept
- Formulaire d'inscription (avec validation de code NCM)
- Connexion sécurisée

### Zone membre
- Profil utilisateur
- Visualisation de l'arbre des membres
- Catalogue de produits
- Paiement en Bitcoin
- Génération de codes NCM

### Zone admin
- Tableau de bord avec métriques
- Gestion des membres
- Gestion des codes NCM
- Gestion des produits
- Gestion des coupons
- Paramètres

## Technologies utilisées

- Next.js
- TypeScript
- Supabase (Auth & Database)
- React Flow (Visualisation de l'arbre)
- BTCPay Server (Paiements Bitcoin)
- TailwindCSS

## Installation

1. Cloner le dépôt
```bash
git clone https://github.com/your-username/iaful-app.git
cd iaful-app
```

2. Installer les dépendances
```bash
npm install
```

3. Configurer les variables d'environnement
Créez un fichier `.env.local` à la racine du projet avec les variables suivantes :
```
NEXT_PUBLIC_SUPABASE_URL=your-supabase-url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key
NEXT_PUBLIC_BTCPAY_SERVER_URL=your-btcpay-server-url
NEXT_PUBLIC_BTCPAY_API_KEY=your-btcpay-api-key
NEXT_PUBLIC_ADMIN_WHATSAPP=your-admin-whatsapp-number
NEXT_PUBLIC_ADMIN_EMAIL=your-admin-email
```

4. Configurer Supabase
Suivez les instructions détaillées dans le fichier [SUPABASE_SETUP.md](./SUPABASE_SETUP.md) pour configurer votre base de données Supabase.

```bash
# Une fois Supabase configuré, exécutez le script de configuration
npm run setup-db
```

5. Lancer le serveur de développement
```bash
npm run dev
```

6. Ouvrir [http://localhost:3000](http://localhost:3000) dans votre navigateur

## Démarrage Rapide (Windows)

Pour un démarrage rapide, plusieurs scripts sont disponibles :

### Installation et démarrage complet

```bash
setup-and-start.bat
```

Ce script tout-en-un :
1. Installe toutes les dépendances
2. Crée un fichier `.env.local` avec des valeurs par défaut
3. Démarre le serveur de développement

### Installation des dépendances uniquement

```bash
install-deps.bat
```

### Démarrage avec données fictives

```bash
start-dev-with-mock.bat
```

Ce script crée un fichier `.env.local` avec des valeurs par défaut et démarre le serveur de développement.

### Configuration des variables d'environnement uniquement

```bash
setup-env.bat
```

Ce script crée un fichier `.env.local` avec des valeurs par défaut pour le développement.

## Résolution des Problèmes

Si vous rencontrez des problèmes lors de l'installation ou de l'exécution de l'application, consultez le guide de dépannage :
[TROUBLESHOOTING.md](./TROUBLESHOOTING.md)

## Mode de développement sans Supabase

Pour faciliter le développement sans avoir à configurer Supabase, l'application inclut un mode de développement avec des données fictives :

### Méthode rapide (Windows)

Exécutez simplement le script `start-dev-with-mock.bat` qui :
1. Crée un fichier `.env.local` avec des identifiants fictifs s'il n'existe pas
2. Démarre le serveur de développement Next.js

### Méthode manuelle

1. Exécutez le script `setup-env.bat` pour créer un fichier `.env.local` avec des identifiants fictifs
   - Ou créez manuellement le fichier `.env.local` avec les valeurs par défaut
2. Exécutez `npm run dev` pour démarrer le serveur de développement

### Identifiants de connexion

Une fois l'application démarrée, vous pouvez vous connecter avec les identifiants suivants :
- Admin : <EMAIL> / admin123
- Membre : <EMAIL> / member123

L'application détectera automatiquement l'absence de configuration Supabase réelle et utilisera des données fictives pour toutes les fonctionnalités.

## Structure de la base de données

### Tables principales

- `members` : Informations sur les membres et liens hiérarchiques
- `ncm_keys` : Codes NCM, propriété et statut
- `products` : Catalogue de produits exclusifs
- `bitcoin_payments` : Suivi des paiements
- `admin_settings` : Paramètres configurables de la plateforme
- `coupons` : Coupons de réduction

Le schéma complet de la base de données est disponible dans le fichier [lib/supabase-schema.sql](./lib/supabase-schema.sql).

## Visualisation de l'arbre des membres

L'application utilise React Flow pour visualiser la structure pyramidale des membres :

- Chaque membre est représenté par un nœud dans l'arbre
- Les relations de parrainage sont représentées par des liens entre les nœuds
- Les nœuds sont colorés en fonction du niveau du membre
- L'administrateur peut voir l'arbre complet dans la section Admin
- Les membres peuvent voir leur propre sous-arbre dans leur profil

## Gestion des NCM

Les codes NCM (Numéro Club de Membre) sont au cœur du système :

- Format : `NCM-[Niveau]-[Hash]-[Checksum]`
- Chaque code est unique et ne peut être utilisé qu'une seule fois
- Les codes sont générés par l'administrateur ou par les membres selon leur niveau
- Le nombre de codes qu'un membre peut générer dépend de son niveau
- Les codes générés sont stockés dans la base de données et peuvent être copiés pour être partagés

## Paiements Bitcoin

L'application intègre un système de paiement en Bitcoin :

- Les prix sont affichés en EUR et en BTC
- Le taux de conversion est mis à jour régulièrement
- Chaque transaction génère une adresse Bitcoin unique
- Les paiements sont suivis et confirmés automatiquement
- L'administrateur peut voir l'historique des paiements

Pour configurer BTCPay Server :

1. Créez un compte sur [BTCPay Server](https://btcpayserver.org/)
2. Configurez un magasin et générez une clé API
3. Ajoutez l'URL et la clé API dans le fichier `.env.local`

## Déploiement

L'application peut être déployée sur Vercel, Netlify ou tout autre service compatible avec Next.js.

```bash
# Construire l'application pour la production
npm run build

# Démarrer le serveur de production
npm run start
```

Pour déployer sur Vercel :

```bash
# Installer Vercel CLI
npm install -g vercel

# Déployer
vercel
```

N'oubliez pas de configurer les variables d'environnement sur votre plateforme de déploiement.

## Sécurité

L'application implémente plusieurs mesures de sécurité :

- Authentification sécurisée via Supabase Auth
- Row Level Security (RLS) pour la base de données
- Validation des codes NCM avec checksums
- Protection contre les attaques CSRF
- Sanitization des entrées utilisateur

## Licence

Tous droits réservés.
