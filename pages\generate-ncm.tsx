import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import Layout from '../components/Layout';
import { AuthContext, supabase } from './_app';
import { generateMultipleNCMKeys, getNCMKeyCountForLevel } from '../lib/ncm';
import { getMemberById, createNCMKeys } from '../lib/supabase';

export default function GenerateNCM() {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [memberData, setMemberData] = useState<any>(null);
  const [generatedKeys, setGeneratedKeys] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      router.push('/login');
      return;
    }

    // Fetch member data
    const fetchMemberData = async () => {
      try {
        setLoading(true);

        const memberData = await getMemberById(user.id);

        if (!memberData) {
          throw new Error('Member data not found');
        }

        setMemberData(memberData);
      } catch (error) {
        console.error('Error fetching member data:', error);
        setError('Error fetching member data. Please try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchMemberData();
  }, [user, router]);

  const handleGenerateKeys = async () => {
    try {
      setGenerating(true);
      setError(null);

      if (!memberData) {
        throw new Error('Member data not found');
      }

      // Get the number of keys the member can generate
      const keyCount = getNCMKeyCountForLevel(memberData.level);

      if (keyCount === 0) {
        throw new Error('You cannot generate NCM keys at your level');
      }

      // Generate the keys
      const nextLevel = memberData.level + 1;
      const keys = generateMultipleNCMKeys(nextLevel, keyCount);

      // Prepare the keys data for database insertion
      const keysData = keys.map(key => ({
        key,
        generated_by_member_id: user.id,
        level: nextLevel,
        used: false,
        created_at: new Date().toISOString(),
      }));

      // Save the keys to the database
      const { data, error } = await supabase
        .from('ncm_keys')
        .insert(keysData)
        .select();

      if (error) {
        throw new Error('Error saving NCM keys to database');
      }

      // Set the generated keys in state
      setGeneratedKeys(keys);
    } catch (error: any) {
      console.error('Error generating keys:', error);
      setError(error.message);
    } finally {
      setGenerating(false);
    }
  };

  if (loading) {
    return (
      <Layout title="Générer des NCM | IAFUL">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center h-64">
            <div className="text-gold-luxurious text-xl">Chargement...</div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Générer des NCM | IAFUL">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-playfair font-bold text-gold-luxurious mb-8">
            Générer des Numéros Club de Membre
          </h1>

          {error && (
            <div className="bg-red-900/50 border border-red-500 text-white-off p-4 rounded-md mb-6">
              {error}
            </div>
          )}

          <div className="card mb-8">
            <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Informations</h2>
            <p className="text-white-off mb-4">
              En tant que membre de niveau {memberData?.level || 'N/A'}, vous pouvez générer{' '}
              <span className="text-gold-luxurious font-bold">
                {getNCMKeyCountForLevel(memberData?.level || 0)}
              </span>{' '}
              NCM de niveau {(memberData?.level || 0) + 1}.
            </p>
            <p className="text-white-off mb-4">
              Ces NCM vous permettront de parrainer de nouveaux membres qui rejoindront votre réseau.
            </p>
            <button
              onClick={handleGenerateKeys}
              disabled={generating || generatedKeys.length > 0}
              className="btn w-full py-3"
            >
              {generating ? 'Génération en cours...' : 'Générer mes NCM'}
            </button>
          </div>

          {generatedKeys.length > 0 && (
            <div className="card">
              <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Vos NCM générés</h2>
              <p className="text-white-off mb-4">
                Voici vos NCM générés. Vous pouvez les partager avec les personnes que vous souhaitez parrainer.
              </p>
              <div className="space-y-4">
                {generatedKeys.map((key, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-gray-anthracite rounded-md">
                    <div>
                      <p className="text-white-off font-medium">{key}</p>
                    </div>
                    <button
                      className="text-sm text-gold-luxurious hover:underline"
                      onClick={() => {
                        navigator.clipboard.writeText(key);
                        alert('NCM copié dans le presse-papier');
                      }}
                    >
                      Copier
                    </button>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
