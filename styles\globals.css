@import url('https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Montserrat:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --black-deep: #000000;
  --gray-anthracite: #1E1E1E;
  --gold-luxurious: #CFAF5A;
  --white-off: #F5F5F5;
}

body {
  background-color: var(--black-deep);
  background-image: url('/background.jpg');
  background-size: cover;
  background-position: center;
  background-repeat: no-repeat;
  background-attachment: fixed;
  color: var(--white-off);
  font-family: 'Montserrat', sans-serif;
  position: relative;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: -1;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  color: var(--gold-luxurious);
}

.btn {
  background-color: var(--gold-luxurious);
  color: var(--black-deep);
  border-radius: 5px;
  padding: 0.5rem 1.5rem;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn:hover {
  box-shadow: 0 0 10px var(--gold-luxurious);
}

.input-field {
  background-color: var(--gray-anthracite);
  border: 1px solid var(--gold-luxurious);
  color: var(--white-off);
  border-radius: 5px;
  padding: 0.5rem 1rem;
}

.card {
  background-color: var(--gray-anthracite);
  border-radius: 5px;
  padding: 1.5rem;
}

/* Custom animations */
@keyframes goldGlow {
  0% {
    box-shadow: 0 0 5px var(--gold-luxurious);
  }
  50% {
    box-shadow: 0 0 15px var(--gold-luxurious);
  }
  100% {
    box-shadow: 0 0 5px var(--gold-luxurious);
  }
}
