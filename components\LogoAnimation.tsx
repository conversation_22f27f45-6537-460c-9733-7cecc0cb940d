import React, { useRef, useState, useEffect, useMemo, Suspense } from 'react';
import { Canvas, useFrame, useLoader } from '@react-three/fiber';
import { TextureLoader } from 'three/src/loaders/TextureLoader';
import { OrbitControls, PerspectiveCamera } from '@react-three/drei';
import * as THREE from 'three';
import { BufferGeometry, BufferAttribute, Points, PointsMaterial } from 'three';

function LogoMesh() {
  const mesh = useRef<THREE.Mesh>(null!);
  const [hovered, setHover] = useState(false);

  // Load texture with error handling
  const texture = useLoader(TextureLoader, '/logo.jpg', (loader) => {
    loader.crossOrigin = 'anonymous';
  });

  // Create material with useMemo to prevent recreation on each render
  const material = useMemo(() => {
    const mat = new THREE.MeshStandardMaterial({
      map: texture,
      emissive: "#CFAF5A",
      emissiveIntensity: 0.2,
      metalness: 0.8,
      roughness: 0.2
    });
    return mat;
  }, [texture]);

  // Update material when hover state changes
  useEffect(() => {
    if (material) {
      material.emissiveIntensity = hovered ? 0.5 : 0.2;
    }
  }, [hovered, material]);

  // Rotate the logo
  useFrame((state, delta) => {
    if (mesh.current) {
      mesh.current.rotation.y += delta * (hovered ? 0.5 : 0.2);
      mesh.current.rotation.x = THREE.MathUtils.lerp(
        mesh.current.rotation.x,
        hovered ? Math.PI / 8 : 0,
        0.1
      );
    }
  });

  return (
    <mesh
      ref={mesh}
      onPointerOver={() => setHover(true)}
      onPointerOut={() => setHover(false)}
    >
      <planeGeometry args={[3, 3]} />
      <primitive object={material} attach="material" />
    </mesh>
  );
}

function FloatingParticles() {
  const particlesRef = useRef<Points>(null!);
  const particleCount = 200;

  // Create particles with useMemo to prevent recreation on each render
  const [positions, colors, sizes, geometry] = useMemo(() => {
    const positions = new Float32Array(particleCount * 3);
    const colors = new Float32Array(particleCount * 3);
    const sizes = new Float32Array(particleCount);

    for (let i = 0; i < particleCount; i++) {
      const i3 = i * 3;
      // Position
      positions[i3] = (Math.random() - 0.5) * 10;
      positions[i3 + 1] = (Math.random() - 0.5) * 10;
      positions[i3 + 2] = (Math.random() - 0.5) * 10;

      // Color - gold particles
      colors[i3] = 0.8 + Math.random() * 0.2; // R
      colors[i3 + 1] = 0.6 + Math.random() * 0.2; // G
      colors[i3 + 2] = 0.3 + Math.random() * 0.2; // B

      // Size
      sizes[i] = Math.random() * 0.1;
    }

    // Create geometry and set attributes
    const geometry = new BufferGeometry();
    geometry.setAttribute('position', new BufferAttribute(positions, 3));
    geometry.setAttribute('color', new BufferAttribute(colors, 3));
    geometry.setAttribute('size', new BufferAttribute(sizes, 1));

    return [positions, colors, sizes, geometry];
  }, []);

  // Animate particles
  useFrame((state, delta) => {
    if (particlesRef.current) {
      const positionAttribute = particlesRef.current.geometry.getAttribute('position');
      const positionArray = positionAttribute.array as Float32Array;

      for (let i = 0; i < particleCount; i++) {
        const i3 = i * 3;

        // Move particles in a circular pattern
        const x = positionArray[i3];
        const z = positionArray[i3 + 2];
        const angle = Math.atan2(z, x) + delta * 0.2;
        const radius = Math.sqrt(x * x + z * z);

        positionArray[i3] = Math.cos(angle) * radius;
        positionArray[i3 + 2] = Math.sin(angle) * radius;

        // Add some vertical movement
        positionArray[i3 + 1] += Math.sin(state.clock.elapsedTime * 0.5 + i) * delta * 0.2;

        // Reset particles that go too far
        if (Math.abs(positionArray[i3 + 1]) > 5) {
          positionArray[i3 + 1] = Math.sign(positionArray[i3 + 1]) * 5;
        }
      }

      positionAttribute.needsUpdate = true;
    }
  });

  // Create material with useMemo
  const material = useMemo(() => {
    return new PointsMaterial({
      size: 0.1,
      vertexColors: true,
      transparent: true,
      blending: THREE.AdditiveBlending,
      sizeAttenuation: true
    });
  }, []);

  return <primitive object={new Points(geometry, material)} ref={particlesRef} />;
}

// Error boundary for Three.js components
class ErrorBoundary extends React.Component<{ children: React.ReactNode }, { hasError: boolean }> {
  constructor(props: { children: React.ReactNode }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    return { hasError: true };
  }

  componentDidCatch(error: any, errorInfo: any) {
    console.error("Three.js error:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="w-full h-full flex items-center justify-center">
          <div className="text-gold-luxurious text-center">
            <p>Unable to load 3D animation.</p>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Scene component to separate from mounting logic
function Scene() {
  return (
    <Canvas dpr={[1, 2]} gl={{ antialias: true, alpha: true }}>
      <PerspectiveCamera makeDefault position={[0, 0, 5]} />
      <ambientLight intensity={0.5} />
      <spotLight position={[10, 10, 10]} angle={0.15} penumbra={1} intensity={1} />
      <pointLight position={[-10, -10, -10]} intensity={0.5} />
      <Suspense fallback={null}>
        <LogoMesh />
        <FloatingParticles />
      </Suspense>
      <OrbitControls enableZoom={false} enablePan={false} />
    </Canvas>
  );
}

export default function LogoAnimation() {
  const [mounted, setMounted] = useState(false);

  // Handle SSR (Next.js)
  useEffect(() => {
    setMounted(true);
    return () => {
      // Cleanup any resources when component unmounts
    };
  }, []);

  if (!mounted) return null;

  return (
    <div className="w-full h-full absolute inset-0 z-10">
      <ErrorBoundary>
        <Scene />
      </ErrorBoundary>
    </div>
  );
}
