import { useState, useEffect, useContext } from 'react';
import { AuthContext } from '../pages/_app';

// DePay Widget types
declare global {
  interface Window {
    DePayWidgets: {
      Payment: (config: any) => void;
    };
  }
}

interface PaymentWidgetProps {
  product: {
    id: string;
    name: string;
    price: number;
    image?: string;
  };
  onSuccess?: (paymentData: any) => void;
  onError?: (error: any) => void;
  onClose?: () => void;
}

export default function PaymentWidget({ 
  product, 
  onSuccess, 
  onError, 
  onClose 
}: PaymentWidgetProps) {
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(false);
  const [memberData, setMemberData] = useState<any>(null);
  const [finalPrice, setFinalPrice] = useState(product.price);
  const [appliedDiscount, setAppliedDiscount] = useState<any>(null);

  useEffect(() => {
    // Load DePay Widget script
    const script = document.createElement('script');
    script.src = 'https://integrate.depay.com/widgets/v12.js';
    script.async = true;
    document.head.appendChild(script);

    return () => {
      document.head.removeChild(script);
    };
  }, []);

  useEffect(() => {
    // Fetch member data and check for discounts
    const fetchMemberData = async () => {
      if (!user) return;

      try {
        const response = await fetch('/api/members/profile', {
          headers: {
            'Authorization': `Bearer ${user.access_token}`,
          },
        });

        if (response.ok) {
          const data = await response.json();
          setMemberData(data);

          // Check for exclusive member discounts
          if (data.is_exclusive) {
            const discountResponse = await fetch('/api/members/available-discounts', {
              headers: {
                'Authorization': `Bearer ${user.access_token}`,
              },
            });

            if (discountResponse.ok) {
              const discounts = await discountResponse.json();
              const applicableDiscount = discounts.find((d: any) => !d.used);
              
              if (applicableDiscount) {
                setAppliedDiscount(applicableDiscount);
                const discountAmount = (product.price * applicableDiscount.discount_pct) / 100;
                setFinalPrice(product.price - discountAmount);
              }
            }
          }
        }
      } catch (error) {
        console.error('Error fetching member data:', error);
      }
    };

    fetchMemberData();
  }, [user, product.price]);

  const handlePayment = async () => {
    if (!window.DePayWidgets) {
      alert('Payment system is loading. Please try again in a moment.');
      return;
    }

    setLoading(true);

    try {
      // Create order in database
      const orderResponse = await fetch('/api/orders/create', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': user ? `Bearer ${user.access_token}` : '',
        },
        body: JSON.stringify({
          product_id: product.id,
          member_id: memberData?.id,
          amount_eur: finalPrice,
          applied_discount: appliedDiscount?.id,
        }),
      });

      if (!orderResponse.ok) {
        throw new Error('Failed to create order');
      }

      const order = await orderResponse.json();

      // Configure DePay Widget
      window.DePayWidgets.Payment({
        integration: process.env.NEXT_PUBLIC_DEPAY_INTEGRATION_ID,
        
        // Dynamic configuration
        payload: {
          order_id: order.id,
          member_id: memberData?.id,
          product_id: product.id,
          discount_id: appliedDiscount?.id,
        },

        // Styling to match IAFUL theme
        style: {
          colors: {
            primary: '#D4AF37',      // IAFUL Gold
            secondary: '#1A1A1A',    // IAFUL Black
            text: '#F5F5F5',         // Off-white
            background: '#0A0A0A',   // Deep black
            buttonText: '#000000',   // Black text on gold buttons
          },
          fontFamily: 'Playfair Display, serif',
          borderRadius: '8px',
          fontSize: '16px',
        },

        // Event handlers
        success: (paymentData: any) => {
          setLoading(false);
          onSuccess?.(paymentData);
        },

        error: (error: any) => {
          setLoading(false);
          onError?.(error);
        },

        close: () => {
          setLoading(false);
          onClose?.();
        },
      });

    } catch (error) {
      setLoading(false);
      console.error('Payment error:', error);
      onError?.(error);
    }
  };

  return (
    <div className="bg-gray-anthracite border border-gold-luxurious/30 rounded-lg p-6">
      {/* Product Information */}
      <div className="flex items-center mb-6">
        {product.image && (
          <img 
            src={product.image} 
            alt={product.name}
            className="w-16 h-16 rounded-lg object-cover mr-4"
          />
        )}
        <div>
          <h3 className="text-xl font-playfair text-gold-luxurious font-bold">
            {product.name}
          </h3>
          <div className="flex items-center space-x-2">
            {appliedDiscount && (
              <span className="text-white-off/70 line-through">
                €{product.price.toFixed(2)}
              </span>
            )}
            <span className="text-2xl font-bold text-gold-luxurious">
              €{finalPrice.toFixed(2)}
            </span>
          </div>
        </div>
      </div>

      {/* Discount Information */}
      {appliedDiscount && (
        <div className="bg-gradient-to-r from-gold-luxurious/10 to-gold-warm/10 border border-gold-luxurious/30 rounded-md p-3 mb-4">
          <div className="flex items-center">
            <span className="text-gold-luxurious text-sm font-medium">
              🎫 Coupon Exclusif Appliqué
            </span>
          </div>
          <div className="text-white-off text-sm">
            -{appliedDiscount.discount_pct}% de réduction • Code: {appliedDiscount.code}
          </div>
        </div>
      )}

      {/* Member Status */}
      {memberData && (
        <div className="bg-gray-anthracite/50 rounded-md p-3 mb-4">
          <div className="flex items-center justify-between">
            <span className="text-white-off/70 text-sm">Membre:</span>
            <div className="flex items-center space-x-2">
              <span className="text-white-off">{memberData.name}</span>
              {memberData.is_exclusive && (
                <span className="px-2 py-1 bg-gold-luxurious/20 text-gold-luxurious text-xs rounded">
                  Exclusif
                </span>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Payment Methods Info */}
      <div className="mb-6">
        <h4 className="text-white-off font-medium mb-3">Méthodes de Paiement Acceptées</h4>
        <div className="grid grid-cols-3 gap-2">
          <div className="bg-gray-anthracite/30 rounded-md p-2 text-center">
            <div className="text-white-off text-xs">USDT</div>
            <div className="text-white-off/70 text-xs">Ethereum</div>
          </div>
          <div className="bg-gray-anthracite/30 rounded-md p-2 text-center">
            <div className="text-white-off text-xs">ETH</div>
            <div className="text-white-off/70 text-xs">Ethereum</div>
          </div>
          <div className="bg-gray-anthracite/30 rounded-md p-2 text-center">
            <div className="text-white-off text-xs">BTC</div>
            <div className="text-white-off/70 text-xs">Bitcoin</div>
          </div>
        </div>
        <p className="text-white-off/60 text-xs mt-2">
          Conversion en temps réel • Paiement sécurisé • Confirmation instantanée
        </p>
      </div>

      {/* Payment Button */}
      <button
        onClick={handlePayment}
        disabled={loading}
        className="btn w-full py-4 bg-gradient-to-r from-gold-luxurious to-gold-warm text-black-deep font-bold text-lg hover:shadow-lg hover:shadow-gold-luxurious/25 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {loading ? (
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-black-deep mr-2"></div>
            Ouverture du paiement...
          </div>
        ) : (
          <div className="flex items-center justify-center">
            <span className="mr-2">💳</span>
            Payer €{finalPrice.toFixed(2)} en Crypto
          </div>
        )}
      </button>

      {/* Security Notice */}
      <div className="mt-4 text-center">
        <p className="text-white-off/60 text-xs">
          🔐 Paiement sécurisé par DePay • Aucune donnée bancaire stockée
        </p>
      </div>
    </div>
  );
}
