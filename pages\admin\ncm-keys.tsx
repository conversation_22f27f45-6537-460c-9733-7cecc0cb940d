import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Layout from '../../components/Layout';
import { AuthContext, supabase } from '../_app';
import { mockDB } from '../../lib/mockAuth';
import { generateMultipleNCMKeys } from '../../lib/ncm';

export default function AdminNCMKeys() {
  const router = useRouter();
  const { user, session, useMockAuth } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [ncmKeys, setNcmKeys] = useState<any[]>([]);
  const [filteredKeys, setFilteredKeys] = useState<any[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterLevel, setFilterLevel] = useState<number | null>(null);
  const [filterUsed, setFilterUsed] = useState<boolean | null>(null);
  const [generatingKeys, setGeneratingKeys] = useState(false);
  const [newKeysForm, setNewKeysForm] = useState({
    level: 1,
    count: 10,
  });

  useEffect(() => {
    // Check if user is authenticated and is admin
    const checkAdmin = async () => {
      if (!user) {
        router.push('/login');
        return;
      }

      try {
        if (useMockAuth) {
          // Use mock data for development
          const memberData = await mockDB.getMemberById(user.id);
          
          if (!memberData || !memberData.is_admin) {
            router.push('/profile');
            return;
          }
          
          // Create mock NCM keys
          const mockKeys = [
            {
              id: 'key-1',
              key: 'NCM-1-ABC123-XYZ1',
              generated_by_member_id: null,
              assigned_to_member_id: 'member-1',
              level: 1,
              used: true,
              created_at: '2025-01-15T10:30:00Z',
              used_at: '2025-01-16T14:20:00Z',
            },
            {
              id: 'key-2',
              key: 'NCM-1-DEF456-UVW2',
              generated_by_member_id: null,
              assigned_to_member_id: 'member-2',
              level: 1,
              used: true,
              created_at: '2025-01-15T10:30:00Z',
              used_at: '2025-01-17T09:15:00Z',
            },
            {
              id: 'key-3',
              key: 'NCM-1-GHI789-RST3',
              generated_by_member_id: null,
              assigned_to_member_id: 'member-3',
              level: 1,
              used: true,
              created_at: '2025-01-15T10:30:00Z',
              used_at: '2025-01-18T16:45:00Z',
            },
            {
              id: 'key-4',
              key: 'NCM-1-JKL012-OPQ4',
              generated_by_member_id: null,
              assigned_to_member_id: null,
              level: 1,
              used: false,
              created_at: '2025-01-15T10:30:00Z',
              used_at: null,
            },
            {
              id: 'key-5',
              key: 'NCM-2-MNO345-NML5',
              generated_by_member_id: 'member-1',
              assigned_to_member_id: 'member-4',
              level: 2,
              used: true,
              created_at: '2025-01-20T11:10:00Z',
              used_at: '2025-01-22T13:30:00Z',
            },
            {
              id: 'key-6',
              key: 'NCM-2-PQR678-KJI6',
              generated_by_member_id: 'member-1',
              assigned_to_member_id: 'member-5',
              level: 2,
              used: true,
              created_at: '2025-01-20T11:10:00Z',
              used_at: '2025-01-23T10:20:00Z',
            },
            {
              id: 'key-7',
              key: 'NCM-2-STU901-HGF7',
              generated_by_member_id: 'member-2',
              assigned_to_member_id: 'member-6',
              level: 2,
              used: true,
              created_at: '2025-01-21T09:45:00Z',
              used_at: '2025-01-24T15:10:00Z',
            },
            {
              id: 'key-8',
              key: 'NCM-2-VWX234-EDC8',
              generated_by_member_id: 'member-1',
              assigned_to_member_id: null,
              level: 2,
              used: false,
              created_at: '2025-01-20T11:10:00Z',
              used_at: null,
            },
            {
              id: 'key-9',
              key: 'NCM-3-YZA567-CBA9',
              generated_by_member_id: 'member-4',
              assigned_to_member_id: 'member-7',
              level: 3,
              used: true,
              created_at: '2025-01-25T14:20:00Z',
              used_at: '2025-01-26T11:30:00Z',
            },
            {
              id: 'key-10',
              key: 'NCM-3-BCD890-ZYX0',
              generated_by_member_id: 'member-4',
              assigned_to_member_id: 'member-8',
              level: 3,
              used: true,
              created_at: '2025-01-25T14:20:00Z',
              used_at: '2025-01-27T16:40:00Z',
            },
          ];
          
          setNcmKeys(mockKeys);
          setFilteredKeys(mockKeys);
        } else {
          // Use Supabase for production
          // Check if user is admin
          const { data, error } = await supabase
            .from('members')
            .select('*')
            .eq('id', user.id)
            .single();

          if (error || !data?.is_admin) {
            // Not an admin, redirect to profile
            router.push('/profile');
            return;
          }

          // Fetch all NCM keys
          const { data: keysData, error: keysError } = await supabase
            .from('ncm_keys')
            .select('*')
            .order('created_at', { ascending: false });

          if (keysError) {
            console.error('Error fetching NCM keys:', keysError);
            return;
          }

          setNcmKeys(keysData || []);
          setFilteredKeys(keysData || []);
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
        router.push('/profile');
      } finally {
        setLoading(false);
      }
    };

    checkAdmin();
  }, [user, router, useMockAuth]);

  // Apply filters when search term or filters change
  useEffect(() => {
    let filtered = [...ncmKeys];
    
    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(key => 
        key.key.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }
    
    // Apply level filter
    if (filterLevel !== null) {
      filtered = filtered.filter(key => key.level === filterLevel);
    }
    
    // Apply used filter
    if (filterUsed !== null) {
      filtered = filtered.filter(key => key.used === filterUsed);
    }
    
    setFilteredKeys(filtered);
  }, [searchTerm, filterLevel, filterUsed, ncmKeys]);

  const handleGenerateKeys = async () => {
    try {
      setGeneratingKeys(true);
      
      // Generate new keys
      const newKeys = generateMultipleNCMKeys(newKeysForm.level, newKeysForm.count);
      
      // Prepare keys data for database
      const keysData = newKeys.map(key => ({
        id: `key-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        key,
        generated_by_member_id: null, // Admin-generated
        assigned_to_member_id: null,
        level: newKeysForm.level,
        used: false,
        created_at: new Date().toISOString(),
        used_at: null,
      }));
      
      if (useMockAuth) {
        // Add to mock data
        setNcmKeys(prev => [...keysData, ...prev]);
      } else {
        // Add to Supabase
        const { data, error } = await supabase
          .from('ncm_keys')
          .insert(keysData)
          .select();
          
        if (error) {
          throw new Error('Error creating NCM keys');
        }
        
        // Update state with new keys
        setNcmKeys(prev => [...(data || []), ...prev]);
      }
      
      // Reset form
      setNewKeysForm({
        level: 1,
        count: 10,
      });
      
      alert(`${newKeysForm.count} clés NCM de niveau ${newKeysForm.level} ont été générées avec succès.`);
    } catch (error) {
      console.error('Error generating keys:', error);
      alert('Erreur lors de la génération des clés NCM.');
    } finally {
      setGeneratingKeys(false);
    }
  };

  const handleFormChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setNewKeysForm(prev => ({
      ...prev,
      [name]: name === 'count' ? parseInt(value) : parseInt(value),
    }));
  };

  const handleCopyKeys = (keys: string[]) => {
    navigator.clipboard.writeText(keys.join('\n'));
    alert('Clés copiées dans le presse-papier');
  };

  if (loading) {
    return (
      <Layout title="Gestion des NCM | IAFUL Admin" isAdmin>
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center h-64">
            <div className="text-gold-luxurious text-xl">Chargement...</div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Gestion des NCM | IAFUL Admin" isAdmin>
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-playfair font-bold text-gold-luxurious mb-8">
            Gestion des Numéros Club de Membre
          </h1>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
            <div className="md:col-span-1">
              <div className="card">
                <h2 className="text-xl font-playfair text-gold-luxurious mb-6">Générer des NCM</h2>
                
                <div className="space-y-4">
                  <div>
                    <label htmlFor="level" className="block text-white-off mb-2">
                      Niveau
                    </label>
                    <select
                      id="level"
                      name="level"
                      value={newKeysForm.level}
                      onChange={handleFormChange}
                      className="input-field w-full"
                    >
                      <option value={1}>Niveau 1</option>
                      <option value={2}>Niveau 2</option>
                      <option value={3}>Niveau 3</option>
                      <option value={4}>Niveau 4</option>
                      <option value={5}>Niveau 5</option>
                      <option value={6}>Niveau 6</option>
                    </select>
                  </div>
                  
                  <div>
                    <label htmlFor="count" className="block text-white-off mb-2">
                      Nombre de clés
                    </label>
                    <input
                      type="number"
                      id="count"
                      name="count"
                      value={newKeysForm.count}
                      onChange={handleFormChange}
                      min={1}
                      max={100}
                      className="input-field w-full"
                    />
                  </div>
                  
                  <button
                    onClick={handleGenerateKeys}
                    disabled={generatingKeys}
                    className="btn w-full py-3"
                  >
                    {generatingKeys ? 'Génération en cours...' : 'Générer des NCM'}
                  </button>
                </div>
              </div>
            </div>
            
            <div className="md:col-span-2">
              <div className="card">
                <h2 className="text-xl font-playfair text-gold-luxurious mb-6">Statistiques des NCM</h2>
                
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
                  <div className="bg-gray-anthracite p-4 rounded-md">
                    <div className="text-white-off/70 text-sm">Total des NCM</div>
                    <div className="text-gold-luxurious text-2xl font-bold">{ncmKeys.length}</div>
                  </div>
                  
                  <div className="bg-gray-anthracite p-4 rounded-md">
                    <div className="text-white-off/70 text-sm">NCM utilisés</div>
                    <div className="text-gold-luxurious text-2xl font-bold">
                      {ncmKeys.filter(key => key.used).length}
                    </div>
                  </div>
                  
                  <div className="bg-gray-anthracite p-4 rounded-md">
                    <div className="text-white-off/70 text-sm">NCM disponibles</div>
                    <div className="text-gold-luxurious text-2xl font-bold">
                      {ncmKeys.filter(key => !key.used).length}
                    </div>
                  </div>
                  
                  <div className="bg-gray-anthracite p-4 rounded-md">
                    <div className="text-white-off/70 text-sm">Taux d'utilisation</div>
                    <div className="text-gold-luxurious text-2xl font-bold">
                      {ncmKeys.length > 0 
                        ? `${Math.round((ncmKeys.filter(key => key.used).length / ncmKeys.length) * 100)}%` 
                        : '0%'}
                    </div>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {[1, 2, 3, 4, 5, 6].map(level => (
                    <div key={level} className="bg-gray-anthracite p-4 rounded-md">
                      <div className="text-white-off/70 text-sm">Niveau {level}</div>
                      <div className="flex justify-between items-center">
                        <div className="text-gold-luxurious text-xl font-bold">
                          {ncmKeys.filter(key => key.level === level).length}
                        </div>
                        <div className="text-white-off text-sm">
                          {ncmKeys.filter(key => key.level === level && key.used).length} utilisés
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          <div className="card">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
              <h2 className="text-xl font-playfair text-gold-luxurious mb-4 md:mb-0">Liste des NCM</h2>
              
              <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4 w-full md:w-auto">
                <input
                  type="text"
                  placeholder="Rechercher un NCM..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="input-field w-full md:w-64"
                />
                
                <select
                  value={filterLevel === null ? '' : filterLevel}
                  onChange={(e) => setFilterLevel(e.target.value === '' ? null : parseInt(e.target.value))}
                  className="input-field w-full md:w-40"
                >
                  <option value="">Tous les niveaux</option>
                  <option value="1">Niveau 1</option>
                  <option value="2">Niveau 2</option>
                  <option value="3">Niveau 3</option>
                  <option value="4">Niveau 4</option>
                  <option value="5">Niveau 5</option>
                  <option value="6">Niveau 6</option>
                </select>
                
                <select
                  value={filterUsed === null ? '' : filterUsed ? 'true' : 'false'}
                  onChange={(e) => setFilterUsed(e.target.value === '' ? null : e.target.value === 'true')}
                  className="input-field w-full md:w-40"
                >
                  <option value="">Tous les statuts</option>
                  <option value="true">Utilisés</option>
                  <option value="false">Disponibles</option>
                </select>
              </div>
            </div>
            
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gold-luxurious/30">
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">NCM</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Niveau</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Statut</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Généré par</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Utilisé par</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Date de création</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredKeys.slice(0, 20).map((key) => (
                    <tr key={key.id} className="border-b border-gray-anthracite">
                      <td className="py-3 px-4 text-white-off font-mono text-sm">{key.key}</td>
                      <td className="py-3 px-4 text-white-off">Niveau {key.level}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs ${key.used ? 'bg-green-900/30 text-green-400' : 'bg-yellow-900/30 text-yellow-400'}`}>
                          {key.used ? 'Utilisé' : 'Disponible'}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-white-off">
                        {key.generated_by_member_id ? 'Membre' : 'Admin'}
                      </td>
                      <td className="py-3 px-4 text-white-off">
                        {key.assigned_to_member_id ? 'Membre' : '-'}
                      </td>
                      <td className="py-3 px-4 text-white-off">
                        {new Date(key.created_at).toLocaleDateString()}
                      </td>
                      <td className="py-3 px-4">
                        <button
                          onClick={() => handleCopyKeys([key.key])}
                          className="text-gold-luxurious hover:underline"
                        >
                          Copier
                        </button>
                      </td>
                    </tr>
                  ))}
                  
                  {filteredKeys.length === 0 && (
                    <tr>
                      <td colSpan={7} className="py-4 text-center text-white-off/70">
                        Aucun NCM trouvé
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
            
            {filteredKeys.length > 20 && (
              <div className="mt-4 text-center text-white-off/70">
                Affichage des 20 premiers résultats sur {filteredKeys.length}
              </div>
            )}
            
            {filteredKeys.filter(key => !key.used).length > 0 && (
              <div className="mt-6 flex justify-end">
                <button
                  onClick={() => handleCopyKeys(filteredKeys.filter(key => !key.used).map(key => key.key))}
                  className="btn px-4 py-2"
                >
                  Copier tous les NCM disponibles
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </Layout>
  );
}
