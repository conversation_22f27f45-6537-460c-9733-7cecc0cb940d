#!/bin/bash

# IAFUL DePay Key Generation Script
# This script generates the RSA keys needed for DePay integration

echo "🔐 Generating RSA Keys for IAFUL DePay Integration..."
echo "=================================================="

# Create keys directory
mkdir -p keys
cd keys

# Generate private key (2048-bit RSA)
echo "1. Generating private key..."
openssl genpkey -algorithm RSA -out private_key.pem -pkeyopt rsa_keygen_bits:2048

if [ $? -eq 0 ]; then
    echo "✅ Private key generated successfully: keys/private_key.pem"
else
    echo "❌ Error generating private key"
    exit 1
fi

# Generate public key from private key
echo "2. Generating public key..."
openssl rsa -pubout -in private_key.pem -out public_key.pem

if [ $? -eq 0 ]; then
    echo "✅ Public key generated successfully: keys/public_key.pem"
else
    echo "❌ Error generating public key"
    exit 1
fi

# Set secure permissions
chmod 600 private_key.pem
chmod 644 public_key.pem

echo ""
echo "🎉 Key generation completed!"
echo "=================================================="
echo ""
echo "📋 Next Steps:"
echo "1. Copy the PRIVATE KEY content to your .env.local file"
echo "2. Copy the PUBLIC KEY content to your DePay dashboard"
echo ""
echo "🔑 PRIVATE KEY (for .env.local):"
echo "================================"
cat private_key.pem
echo ""
echo "🔑 PUBLIC KEY (for DePay dashboard):"
echo "===================================="
cat public_key.pem
echo ""
echo "⚠️  SECURITY WARNING:"
echo "- Keep the private key SECRET and secure"
echo "- Only share the public key with DePay"
echo "- Never commit private keys to version control"
echo ""
echo "📁 Files created:"
echo "- keys/private_key.pem (KEEP SECRET)"
echo "- keys/public_key.pem (share with DePay)"
