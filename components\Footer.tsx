import Link from 'next/link';

type FooterProps = {
  isAdmin?: boolean;
};

export default function Footer({ isAdmin = false }: FooterProps) {
  return (
    <footer className="bg-gray-anthracite py-8">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row justify-between items-center">
          <div className="mb-4 md:mb-0">
            <span className="text-xl font-playfair font-bold text-gold-luxurious">
              {isAdmin ? 'IAFUL Admin' : 'IAFUL'}
            </span>
          </div>
          <div className="text-white-off text-sm">
            &copy; {new Date().getFullYear()} IAFUL. Tous droits réservés.
          </div>
        </div>
      </div>
    </footer>
  );
}
