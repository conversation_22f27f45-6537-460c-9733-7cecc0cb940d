import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import Layout from '../../components/Layout';
import { AuthContext, supabase } from '../_app';
import { generateMultipleNCMKeys } from '../../lib/ncm';
import { createNCMKeys } from '../../lib/supabase';

export default function AdminGenerateNCM() {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [generating, setGenerating] = useState(false);
  const [generatedKeys, setGeneratedKeys] = useState<string[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    level: 1,
    count: 10,
  });

  useEffect(() => {
    // Check if user is authenticated and is admin
    const checkAdmin = async () => {
      if (!user) {
        router.push('/login');
        return;
      }

      try {
        // Check if user is admin
        const { data, error } = await supabase
          .from('members')
          .select('is_admin')
          .eq('id', user.id)
          .single();

        if (error || !data?.is_admin) {
          // Not an admin, redirect to profile
          router.push('/profile');
          return;
        }
      } catch (error) {
        console.error('Error checking admin status:', error);
        router.push('/profile');
      } finally {
        setLoading(false);
      }
    };

    checkAdmin();
  }, [user, router]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: parseInt(value),
    });
  };

  const handleGenerateKeys = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      setGenerating(true);
      setError(null);

      if (formData.level < 1 || formData.level > 6) {
        throw new Error('Level must be between 1 and 6');
      }

      if (formData.count < 1 || formData.count > 100) {
        throw new Error('Count must be between 1 and 100');
      }

      // Generate the keys
      const keys = generateMultipleNCMKeys(formData.level, formData.count);

      // Prepare the keys data for database insertion
      const keysData = keys.map(key => ({
        key,
        generated_by_member_id: null, // Admin-generated keys have no generating member
        level: formData.level,
        used: false,
        created_at: new Date().toISOString(),
      }));

      // Save the keys to the database
      const { data, error: insertError } = await supabase
        .from('ncm_keys')
        .insert(keysData)
        .select();

      if (insertError) {
        throw new Error('Error saving NCM keys to database');
      }

      // Set the generated keys in state
      setGeneratedKeys(keys);
    } catch (error: any) {
      console.error('Error generating keys:', error);
      setError(error.message);
    } finally {
      setGenerating(false);
    }
  };

  if (loading) {
    return (
      <Layout title="Générer des NCM | IAFUL Admin" isAdmin>
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center h-64">
            <div className="text-gold-luxurious text-xl">Chargement...</div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Générer des NCM | IAFUL Admin" isAdmin>
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-3xl font-playfair font-bold text-gold-luxurious mb-8">
            Générer des Numéros Club de Membre
          </h1>

          {error && (
            <div className="bg-red-900/50 border border-red-500 text-white-off p-4 rounded-md mb-6">
              {error}
            </div>
          )}

          <div className="card mb-8">
            <h2 className="text-xl font-playfair text-gold-luxurious mb-4">Générer des NCM</h2>
            <form onSubmit={handleGenerateKeys}>
              <div className="mb-4">
                <label htmlFor="level" className="block text-white-off mb-2">
                  Niveau
                </label>
                <select
                  id="level"
                  name="level"
                  value={formData.level}
                  onChange={handleChange}
                  className="input-field w-full"
                >
                  <option value={1}>Niveau 1</option>
                  <option value={2}>Niveau 2</option>
                  <option value={3}>Niveau 3</option>
                  <option value={4}>Niveau 4</option>
                  <option value={5}>Niveau 5</option>
                  <option value={6}>Niveau 6</option>
                </select>
              </div>

              <div className="mb-6">
                <label htmlFor="count" className="block text-white-off mb-2">
                  Nombre de NCM à générer
                </label>
                <input
                  type="number"
                  id="count"
                  name="count"
                  value={formData.count}
                  onChange={handleChange}
                  min={1}
                  max={100}
                  className="input-field w-full"
                />
              </div>

              <button
                type="submit"
                disabled={generating}
                className="btn w-full py-3"
              >
                {generating ? 'Génération en cours...' : 'Générer les NCM'}
              </button>
            </form>
          </div>

          {generatedKeys.length > 0 && (
            <div className="card">
              <h2 className="text-xl font-playfair text-gold-luxurious mb-4">NCM générés</h2>
              <p className="text-white-off mb-4">
                Voici les NCM générés. Vous pouvez les distribuer aux membres.
              </p>
              <div className="space-y-4">
                {generatedKeys.map((key, index) => (
                  <div key={index} className="flex justify-between items-center p-3 bg-gray-anthracite rounded-md">
                    <div>
                      <p className="text-white-off font-medium">{key}</p>
                    </div>
                    <button
                      className="text-sm text-gold-luxurious hover:underline"
                      onClick={() => {
                        navigator.clipboard.writeText(key);
                        alert('NCM copié dans le presse-papier');
                      }}
                    >
                      Copier
                    </button>
                  </div>
                ))}
              </div>
              <div className="mt-6">
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(generatedKeys.join('\n'));
                    alert('Tous les NCM copiés dans le presse-papier');
                  }}
                  className="btn w-full py-3"
                >
                  Copier tous les NCM
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </Layout>
  );
}
