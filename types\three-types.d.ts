import * as THREE from 'three';

declare module 'three' {
  interface BufferGeometry {
    attributes: {
      position: THREE.BufferAttribute;
      color?: THREE.BufferAttribute;
      size?: THREE.BufferAttribute;
      [key: string]: THREE.BufferAttribute | undefined;
    };
  }
}

declare module '@react-three/fiber' {
  interface ThreeElements {
    bufferAttribute: JSX.IntrinsicElements['bufferAttribute'] & {
      attach?: string;
      array?: Float32Array;
      count?: number;
      itemSize?: number;
    };
  }
}
