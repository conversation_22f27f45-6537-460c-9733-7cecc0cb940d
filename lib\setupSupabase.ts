import { supabase } from './supabase';
import fs from 'fs';
import path from 'path';
import { generateMultipleNCMKeys } from './ncm';

/**
 * Setup the Supabase database schema
 * This function should be run once to set up the database
 */
export async function setupSupabaseSchema() {
  try {
    console.log('Setting up Supabase schema...');
    
    // Read the SQL schema file
    const schemaPath = path.join(process.cwd(), 'lib', 'supabase-schema.sql');
    const schema = fs.readFileSync(schemaPath, 'utf8');
    
    // Split the schema into individual statements
    const statements = schema
      .split(';')
      .map(statement => statement.trim())
      .filter(statement => statement.length > 0);
    
    // Execute each statement
    for (const statement of statements) {
      const { error } = await supabase.rpc('exec_sql', { sql: statement });
      
      if (error) {
        console.error('Error executing SQL statement:', error);
        console.error('Statement:', statement);
      }
    }
    
    console.log('Supabase schema setup complete!');
    return true;
  } catch (error) {
    console.error('Error setting up Supabase schema:', error);
    return false;
  }
}

/**
 * Create an admin user
 * @param email Admin email
 * @param password Admin password
 * @param name Admin name
 */
export async function createAdminUser(email: string, password: string, name: string) {
  try {
    console.log('Creating admin user...');
    
    // Sign up the user
    const { data: authData, error: authError } = await supabase.auth.signUp({
      email,
      password,
    });
    
    if (authError) {
      throw new Error(`Error creating admin user: ${authError.message}`);
    }
    
    if (!authData.user) {
      throw new Error('No user returned from signUp');
    }
    
    // Create the admin member
    const { data: memberData, error: memberError } = await supabase
      .from('members')
      .insert([
        {
          auth_id: authData.user.id,
          name,
          email,
          level: 0,
          is_admin: true,
          ncm_key_used: 'ADMIN',
        },
      ])
      .select()
      .single();
    
    if (memberError) {
      throw new Error(`Error creating admin member: ${memberError.message}`);
    }
    
    console.log('Admin user created successfully!');
    return memberData;
  } catch (error) {
    console.error('Error creating admin user:', error);
    return null;
  }
}

/**
 * Generate initial NCM keys for the admin
 * @param adminId Admin member ID
 * @param count Number of keys to generate
 */
export async function generateInitialNCMKeys(adminId: string, count: number = 10) {
  try {
    console.log(`Generating ${count} initial NCM keys...`);
    
    // Generate the keys
    const keys = generateMultipleNCMKeys(1, count);
    
    // Prepare the keys data
    const keysData = keys.map(key => ({
      key,
      level: 1,
      generated_by_member_id: adminId,
      used: false,
    }));
    
    // Insert the keys
    const { data, error } = await supabase
      .from('ncm_keys')
      .insert(keysData)
      .select();
    
    if (error) {
      throw new Error(`Error creating NCM keys: ${error.message}`);
    }
    
    console.log(`${data?.length || 0} NCM keys generated successfully!`);
    return data;
  } catch (error) {
    console.error('Error generating initial NCM keys:', error);
    return null;
  }
}

/**
 * Create sample products
 */
export async function createSampleProducts() {
  try {
    console.log('Creating sample products...');
    
    const products = [
      {
        name: 'Royal OG',
        description: 'Une variété premium avec des notes terreuses et boisées.',
        image_url: '/royal_og.jpg',
        price_eur: 120,
        price_btc: 0.0032,
        available: true,
      },
      {
        name: 'Lemon Octane',
        description: 'Saveurs d'agrumes intenses avec une puissance remarquable.',
        image_url: '/lemon_octane.jpg',
        price_eur: 140,
        price_btc: 0.0037,
        available: true,
      },
      {
        name: 'Bubble Kush',
        description: 'Arômes sucrés et fruités avec des effets relaxants profonds.',
        image_url: '/bubble_kush.jpg',
        price_eur: 130,
        price_btc: 0.0035,
        available: true,
      },
      {
        name: 'Easy Weed',
        description: 'Parfaite pour les débutants, avec un profil équilibré.',
        image_url: '/fleur-easy_weed.jpg',
        price_eur: 110,
        price_btc: 0.0029,
        available: true,
      },
    ];
    
    // Insert the products
    const { data, error } = await supabase
      .from('products')
      .insert(products)
      .select();
    
    if (error) {
      throw new Error(`Error creating products: ${error.message}`);
    }
    
    console.log(`${data?.length || 0} products created successfully!`);
    return data;
  } catch (error) {
    console.error('Error creating sample products:', error);
    return null;
  }
}

/**
 * Initialize the Supabase database
 * This function sets up the schema, creates an admin user, generates initial NCM keys, and creates sample products
 */
export async function initializeSupabase(adminEmail: string, adminPassword: string, adminName: string) {
  try {
    // Setup schema
    const schemaSetup = await setupSupabaseSchema();
    
    if (!schemaSetup) {
      throw new Error('Failed to set up schema');
    }
    
    // Create admin user
    const admin = await createAdminUser(adminEmail, adminPassword, adminName);
    
    if (!admin) {
      throw new Error('Failed to create admin user');
    }
    
    // Generate initial NCM keys
    const keys = await generateInitialNCMKeys(admin.id);
    
    if (!keys) {
      throw new Error('Failed to generate initial NCM keys');
    }
    
    // Create sample products
    const products = await createSampleProducts();
    
    if (!products) {
      throw new Error('Failed to create sample products');
    }
    
    console.log('Supabase initialization complete!');
    return true;
  } catch (error) {
    console.error('Error initializing Supabase:', error);
    return false;
  }
}
