import React, { useEffect, useRef, useState } from 'react';

interface ScrollAnimationProps {
  children: React.ReactNode;
  animation: 'fade-up' | 'fade-down' | 'fade-left' | 'fade-right' | 'zoom-in' | 'zoom-out';
  delay?: number;
  threshold?: number;
  className?: string;
}

export default function ScrollAnimation({
  children,
  animation,
  delay = 0,
  threshold = 0.1,
  className = '',
}: ScrollAnimationProps) {
  const [isVisible, setIsVisible] = useState(false);
  const ref = useRef<HTMLDivElement>(null);

  useEffect(() => {
    // Skip animation if user prefers reduced motion
    const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
    if (prefersReducedMotion) {
      setIsVisible(true);
      return;
    }

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      {
        threshold,
        rootMargin: '0px 0px -100px 0px',
      }
    );

    const currentRef = ref.current;
    if (currentRef) {
      observer.observe(currentRef);
    }

    return () => {
      if (currentRef) {
        observer.unobserve(currentRef);
      }
    };
  }, [threshold]);

  // Define animation classes
  const animationClasses = {
    'fade-up': 'translate-y-16 opacity-0',
    'fade-down': '-translate-y-16 opacity-0',
    'fade-left': 'translate-x-16 opacity-0',
    'fade-right': '-translate-x-16 opacity-0',
    'zoom-in': 'scale-90 opacity-0',
    'zoom-out': 'scale-110 opacity-0',
  };

  const transitionStyles = {
    transition: `transform 0.8s ease-out ${delay}s, opacity 0.8s ease-out ${delay}s`,
  };

  return (
    <div
      ref={ref}
      className={`transform ${isVisible ? '' : animationClasses[animation]} ${className}`}
      style={isVisible ? { ...transitionStyles } : { ...transitionStyles, opacity: 0 }}
    >
      {children}
    </div>
  );
}
