import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import dynamic from 'next/dynamic';
import Layout from '../../components/Layout';
import { AuthContext, supabase } from '../_app';

// Dynamically import the MemberTree component to avoid SSR issues with ReactFlow
const MemberTree = dynamic(() => import('../../components/MemberTree'), {
  ssr: false,
  loading: () => (
    <div className="bg-gray-anthracite border border-gold-luxurious/30 rounded-md p-4 h-96 flex items-center justify-center">
      <p className="text-white-off/70">Chargement de la visualisation...</p>
    </div>
  ),
});

export default function AdminMemberTree() {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [adminData, setAdminData] = useState<any>(null);
  const [allMembers, setAllMembers] = useState<any[]>([]);
  const [selectedLevel, setSelectedLevel] = useState<number | null>(null);
  const [searchTerm, setSearchTerm] = useState('');

  useEffect(() => {
    // Check if user is authenticated and is admin
    const checkAdmin = async () => {
      if (!user) {
        router.push('/login');
        return;
      }

      try {
        // Check if user is admin
        const { data, error } = await supabase
          .from('members')
          .select('*')
          .eq('auth_id', user.id)
          .single();

        if (error || !data?.is_admin) {
          // Not an admin, redirect to profile
          router.push('/profile');
          return;
        }

        setAdminData(data);

        // Fetch all members for the complete pyramid structure
        const { data: membersData, error: membersError } = await supabase
          .from('members')
          .select('*')
          .order('level', { ascending: true });

        if (membersError) {
          console.error('Error fetching members:', membersError);
          return;
        }

        // Set the members data from the database
        setAllMembers(membersData || []);
      } catch (error) {
        console.error('Error checking admin status:', error);
        router.push('/profile');
      } finally {
        setLoading(false);
      }
    };

    checkAdmin();
  }, [user, router]);

  // Filter members based on search and level
  const filteredMembers = allMembers.filter(member => {
    const matchesSearch = searchTerm === '' ||
      member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      member.email.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesLevel = selectedLevel === null || member.level === selectedLevel;

    return matchesSearch && matchesLevel;
  });

  // Get the root member (admin)
  const rootMember = allMembers.find(m => m.level === 0) || adminData;

  if (loading) {
    return (
      <Layout title="Structure des Membres | IAFUL Admin" isAdmin>
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center h-64">
            <div className="text-gold-luxurious text-xl">Chargement...</div>
          </div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title="Structure des Membres | IAFUL Admin" isAdmin>
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <h1 className="text-3xl font-playfair font-bold text-gold-luxurious mb-8">
            Structure Pyramidale des Membres
          </h1>

          <div className="card mb-8">
            <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-6">
              <div className="mb-4 md:mb-0">
                <h2 className="text-xl font-playfair text-gold-luxurious">Filtres</h2>
              </div>
              <div className="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
                <div>
                  <input
                    type="text"
                    placeholder="Rechercher un membre..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="input-field w-full md:w-64"
                  />
                </div>
                <div>
                  <select
                    value={selectedLevel === null ? '' : selectedLevel}
                    onChange={(e) => setSelectedLevel(e.target.value === '' ? null : parseInt(e.target.value))}
                    className="input-field w-full md:w-48"
                  >
                    <option value="">Tous les niveaux</option>
                    <option value="0">Niveau 0 (Admin)</option>
                    <option value="1">Niveau 1 (10 NCM)</option>
                    <option value="2">Niveau 2 (8 NCM)</option>
                    <option value="3">Niveau 3 (6 NCM)</option>
                    <option value="4">Niveau 4 (4 NCM)</option>
                    <option value="5">Niveau 5 (2 NCM)</option>
                    <option value="6">Niveau 6</option>
                  </select>
                </div>
              </div>
            </div>

            <div className="mb-6">
              <h3 className="text-lg font-playfair text-gold-luxurious mb-4">Statistiques</h3>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="bg-gray-anthracite p-4 rounded-md">
                  <div className="text-white-off/70 text-sm">Total des membres</div>
                  <div className="text-gold-luxurious text-2xl font-bold">{allMembers.length}</div>
                </div>
                <div className="bg-gray-anthracite p-4 rounded-md">
                  <div className="text-white-off/70 text-sm">Niveau 0 (Admin)</div>
                  <div className="text-gold-luxurious text-2xl font-bold">
                    {allMembers.filter(m => m.level === 0).length}
                  </div>
                </div>
                <div className="bg-gray-anthracite p-4 rounded-md">
                  <div className="text-white-off/70 text-sm">Niveau 1 (10 NCM)</div>
                  <div className="text-gold-luxurious text-2xl font-bold">
                    {allMembers.filter(m => m.level === 1).length}
                  </div>
                </div>
                <div className="bg-gray-anthracite p-4 rounded-md">
                  <div className="text-white-off/70 text-sm">Niveau 2 (8 NCM)</div>
                  <div className="text-gold-luxurious text-2xl font-bold">
                    {allMembers.filter(m => m.level === 2).length}
                  </div>
                </div>
                <div className="bg-gray-anthracite p-4 rounded-md">
                  <div className="text-white-off/70 text-sm">Niveau 3 (6 NCM)</div>
                  <div className="text-gold-luxurious text-2xl font-bold">
                    {allMembers.filter(m => m.level === 3).length}
                  </div>
                </div>
                <div className="bg-gray-anthracite p-4 rounded-md">
                  <div className="text-white-off/70 text-sm">Niveau 4 (4 NCM)</div>
                  <div className="text-gold-luxurious text-2xl font-bold">
                    {allMembers.filter(m => m.level === 4).length}
                  </div>
                </div>
                <div className="bg-gray-anthracite p-4 rounded-md">
                  <div className="text-white-off/70 text-sm">Niveau 5 (2 NCM)</div>
                  <div className="text-gold-luxurious text-2xl font-bold">
                    {allMembers.filter(m => m.level === 5).length}
                  </div>
                </div>
                <div className="bg-gray-anthracite p-4 rounded-md">
                  <div className="text-white-off/70 text-sm">Niveau 6</div>
                  <div className="text-gold-luxurious text-2xl font-bold">
                    {allMembers.filter(m => m.level === 6).length}
                  </div>
                </div>
              </div>
            </div>

            <div>
              <h3 className="text-lg font-playfair text-gold-luxurious mb-4">Visualisation de la Structure Pyramidale</h3>
              {rootMember && (
                <MemberTree rootMember={rootMember} members={filteredMembers} />
              )}
            </div>
          </div>

          <div className="card">
            <h2 className="text-xl font-playfair text-gold-luxurious mb-6">Liste des Membres</h2>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gold-luxurious/30">
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Nom</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Email</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Niveau</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Parrain</th>
                    <th className="text-left py-3 px-4 text-gold-luxurious/70">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredMembers.map((member) => {
                    const referrer = allMembers.find(m => m.id === member.referrer_id);

                    return (
                      <tr key={member.id} className="border-b border-gray-anthracite">
                        <td className="py-3 px-4 text-white-off">{member.name}</td>
                        <td className="py-3 px-4 text-white-off">{member.email}</td>
                        <td className="py-3 px-4 text-white-off">
                          {member.level === 0 ? 'Admin' : `Niveau ${member.level}`}
                        </td>
                        <td className="py-3 px-4 text-white-off">
                          {referrer ? referrer.name : '-'}
                        </td>
                        <td className="py-3 px-4">
                          <button
                            onClick={() => router.push(`/admin/members/${member.id}`)}
                            className="text-gold-luxurious hover:underline"
                          >
                            Détails
                          </button>
                        </td>
                      </tr>
                    );
                  })}

                  {filteredMembers.length === 0 && (
                    <tr>
                      <td colSpan={5} className="py-4 text-center text-white-off/70">
                        Aucun membre trouvé
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </Layout>
  );
}
