/**
 * Exclusive NCM Key Generation System
 * Generates secure 9-character binary-based keys with checksum for Membre Exclusif category
 */

/**
 * Generate a secure random binary string of specified length
 * @param length - Length of the binary string
 * @returns Binary string
 */
function generateSecureBinary(length: number): string {
  const chars = '01';
  let result = '';
  
  // Use crypto.getRandomValues for secure random generation
  if (typeof window !== 'undefined' && window.crypto) {
    const array = new Uint8Array(length);
    window.crypto.getRandomValues(array);
    for (let i = 0; i < length; i++) {
      result += chars[array[i] % 2];
    }
  } else if (typeof require !== 'undefined') {
    // Node.js environment
    const crypto = require('crypto');
    const bytes = crypto.randomBytes(length);
    for (let i = 0; i < length; i++) {
      result += chars[bytes[i] % 2];
    }
  } else {
    // Fallback to Math.random (less secure)
    for (let i = 0; i < length; i++) {
      result += chars[Math.floor(Math.random() * 2)];
    }
  }
  
  return result;
}

/**
 * Calculate checksum for a binary string using a simple algorithm
 * @param binaryString - Input binary string
 * @returns Single character checksum
 */
function calculateChecksum(binaryString: string): string {
  let sum = 0;
  
  // Sum all binary digits
  for (let i = 0; i < binaryString.length; i++) {
    sum += parseInt(binaryString[i]);
  }
  
  // Add position-weighted sum for more complexity
  for (let i = 0; i < binaryString.length; i++) {
    sum += parseInt(binaryString[i]) * (i + 1);
  }
  
  // Convert to a single hex character (0-F)
  const checksum = (sum % 16).toString(16).toUpperCase();
  
  return checksum;
}

/**
 * Validate an exclusive NCM key format and checksum
 * @param key - The NCM key to validate
 * @returns True if valid, false otherwise
 */
export function validateExclusiveNCMKey(key: string): boolean {
  // Check format: EXC-XXXXXXXXX-C (EXC + 9 binary digits + checksum)
  const pattern = /^EXC-[01]{9}-[0-9A-F]$/;
  
  if (!pattern.test(key)) {
    return false;
  }
  
  // Extract binary part and checksum
  const parts = key.split('-');
  const binaryPart = parts[1];
  const providedChecksum = parts[2];
  
  // Calculate expected checksum
  const expectedChecksum = calculateChecksum(binaryPart);
  
  return providedChecksum === expectedChecksum;
}

/**
 * Generate a single exclusive NCM key
 * @returns Exclusive NCM key in format EXC-XXXXXXXXX-C
 */
export function generateExclusiveNCMKey(): string {
  // Generate 9-character binary string
  const binaryPart = generateSecureBinary(9);
  
  // Calculate checksum
  const checksum = calculateChecksum(binaryPart);
  
  // Format: EXC-XXXXXXXXX-C
  return `EXC-${binaryPart}-${checksum}`;
}

/**
 * Generate multiple exclusive NCM keys
 * @param count - Number of keys to generate
 * @returns Array of exclusive NCM keys
 */
export function generateMultipleExclusiveNCMKeys(count: number): string[] {
  const keys: string[] = [];
  const usedKeys = new Set<string>();
  
  while (keys.length < count) {
    const key = generateExclusiveNCMKey();
    
    // Ensure uniqueness
    if (!usedKeys.has(key)) {
      keys.push(key);
      usedKeys.add(key);
    }
  }
  
  return keys;
}

/**
 * Convert binary string to decimal for display purposes
 * @param binaryString - Binary string to convert
 * @returns Decimal representation
 */
export function binaryToDecimal(binaryString: string): number {
  return parseInt(binaryString, 2);
}

/**
 * Get key statistics for analysis
 * @param key - Exclusive NCM key
 * @returns Key statistics object
 */
export function getExclusiveKeyStats(key: string): {
  isValid: boolean;
  binaryPart: string;
  checksum: string;
  decimalValue: number;
  onesCount: number;
  zerosCount: number;
} {
  const isValid = validateExclusiveNCMKey(key);
  
  if (!isValid) {
    return {
      isValid: false,
      binaryPart: '',
      checksum: '',
      decimalValue: 0,
      onesCount: 0,
      zerosCount: 0
    };
  }
  
  const parts = key.split('-');
  const binaryPart = parts[1];
  const checksum = parts[2];
  const decimalValue = binaryToDecimal(binaryPart);
  const onesCount = (binaryPart.match(/1/g) || []).length;
  const zerosCount = (binaryPart.match(/0/g) || []).length;
  
  return {
    isValid,
    binaryPart,
    checksum,
    decimalValue,
    onesCount,
    zerosCount
  };
}

/**
 * Generate exclusive NCM keys with specific constraints
 * @param count - Number of keys to generate
 * @param minOnes - Minimum number of 1s in binary part (optional)
 * @param maxOnes - Maximum number of 1s in binary part (optional)
 * @returns Array of exclusive NCM keys meeting constraints
 */
export function generateConstrainedExclusiveNCMKeys(
  count: number,
  minOnes?: number,
  maxOnes?: number
): string[] {
  const keys: string[] = [];
  const usedKeys = new Set<string>();
  let attempts = 0;
  const maxAttempts = count * 100; // Prevent infinite loops
  
  while (keys.length < count && attempts < maxAttempts) {
    const key = generateExclusiveNCMKey();
    attempts++;
    
    if (usedKeys.has(key)) {
      continue;
    }
    
    // Check constraints if provided
    if (minOnes !== undefined || maxOnes !== undefined) {
      const stats = getExclusiveKeyStats(key);
      
      if (minOnes !== undefined && stats.onesCount < minOnes) {
        continue;
      }
      
      if (maxOnes !== undefined && stats.onesCount > maxOnes) {
        continue;
      }
    }
    
    keys.push(key);
    usedKeys.add(key);
  }
  
  return keys;
}

/**
 * Batch validate multiple exclusive NCM keys
 * @param keys - Array of keys to validate
 * @returns Array of validation results
 */
export function batchValidateExclusiveNCMKeys(keys: string[]): Array<{
  key: string;
  isValid: boolean;
  error?: string;
}> {
  return keys.map(key => {
    try {
      const isValid = validateExclusiveNCMKey(key);
      return {
        key,
        isValid,
        error: isValid ? undefined : 'Invalid format or checksum'
      };
    } catch (error) {
      return {
        key,
        isValid: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  });
}
