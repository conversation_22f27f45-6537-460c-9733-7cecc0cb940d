import { initializeSupabase } from '../lib/setupSupabase';
import dotenv from 'dotenv';
import path from 'path';
import readline from 'readline';

// Load environment variables from .env.local
dotenv.config({ path: path.resolve(process.cwd(), '.env.local') });

// Create readline interface
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

// Prompt for admin credentials
async function promptForCredentials(): Promise<{ email: string; password: string; name: string }> {
  return new Promise((resolve) => {
    rl.question('Enter admin email: ', (email) => {
      rl.question('Enter admin password: ', (password) => {
        rl.question('Enter admin name: ', (name) => {
          resolve({ email, password, name });
        });
      });
    });
  });
}

// Main function
async function main() {
  console.log('IAFUL - Supabase Setup Script');
  console.log('-----------------------------');
  
  // Check if Supabase URL and key are set
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('Error: Supabase URL and key must be set in .env.local');
    console.log('Please create a .env.local file with the following variables:');
    console.log('NEXT_PUBLIC_SUPABASE_URL=your-supabase-url');
    console.log('NEXT_PUBLIC_SUPABASE_ANON_KEY=your-supabase-anon-key');
    process.exit(1);
  }
  
  console.log('Supabase URL and key found in .env.local');
  
  // Prompt for admin credentials
  const { email, password, name } = await promptForCredentials();
  
  // Initialize Supabase
  console.log('Initializing Supabase...');
  const success = await initializeSupabase(email, password, name);
  
  if (success) {
    console.log('Supabase initialization complete!');
    console.log(`Admin user created with email: ${email}`);
    console.log('You can now log in to the application with these credentials.');
  } else {
    console.error('Supabase initialization failed.');
    console.log('Please check the error messages above and try again.');
  }
  
  // Close readline interface
  rl.close();
}

// Run the main function
main().catch((error) => {
  console.error('Error:', error);
  process.exit(1);
});
