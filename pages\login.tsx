import Head from 'next/head';
import Link from 'next/link';
import { useState, useContext } from 'react';
import { useRouter } from 'next/router';
import { supabase, AuthContext } from './_app';
import BackgroundImage from '../components/BackgroundImage';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

export default function Login() {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  });

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Sign in with Supabase
      const { data, error } = await supabase.auth.signInWithPassword({
        email: formData.email,
        password: formData.password,
      });

      if (error) {
        throw new Error(error.message);
      }

      if (!data.user) {
        throw new Error('No user returned from signInWithPassword');
      }

      // Check if user is admin
      const { data: memberData, error: memberError } = await supabase
        .from('members')
        .select('is_admin')
        .eq('id', data.user.id)
        .single();

      if (memberError) {
        throw new Error('Error fetching member data');
      }

      const isAdmin = memberData?.is_admin || false;

      // Redirect based on user role
      if (isAdmin) {
        router.push('/admin');
      } else {
        router.push('/profile');
      }
    } catch (error: any) {
      console.error('Login error:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen bg-black-deep relative">
      {/* Background image */}
      <BackgroundImage opacity={80} imagePath="/background.jpg" />

      <Head>
        <title>Connexion </title>
        <meta name="description" content="IAFUL - Le Club Privé Réservé à une Élite Sélectionnée" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Navbar />

      <main className="pt-24 pb-20 relative z-10">
        <div className="container mx-auto px-4">
          <div className="max-w-md mx-auto">
            <h1 className="text-3xl font-playfair font-bold text-gold-luxurious mb-8 text-center">
              Connexion
            </h1>

            {error && (
              <div className="bg-red-900/50 border border-red-500 text-white-off p-4 rounded-md mb-6">
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit} className="card">
              <div className="mb-4">
                <label htmlFor="email" className="block text-white-off mb-2">
                  Email
                </label>
                <input
                  type="email"
                  id="email"
                  name="email"
                  value={formData.email}
                  onChange={handleChange}
                  required
                  className="input-field w-full"
                />
              </div>

              <div className="mb-6">
                <label htmlFor="password" className="block text-white-off mb-2">
                  Mot de passe
                </label>
                <input
                  type="password"
                  id="password"
                  name="password"
                  value={formData.password}
                  onChange={handleChange}
                  required
                  className="input-field w-full"
                />
                <div className="flex justify-end mt-2">
                  <Link href="/forgot-password" className="text-sm text-gold-luxurious hover:underline">
                    Mot de passe oublié ?
                  </Link>
                </div>
              </div>

              <button
                type="submit"
                className="btn w-full py-3"
                disabled={loading}
              >
                {loading ? 'Connexion en cours...' : 'Se connecter'}
              </button>
            </form>

            <p className="text-white-off text-center mt-6">
              Pas encore membre ?{' '}
              <Link href="/register" className="text-gold-luxurious hover:underline">
                S'inscrire
              </Link>
            </p>
          </div>
        </div>
      </main>

      <Footer />
    </div>
  );
}
