// Mock Authentication and Database System for IAFUL
// This file provides a complete mock implementation for testing the application

import { generateNCMKey } from './ncm';

// Mock data types based on the database schema
export interface MockMember {
  id: string;
  auth_id: string;
  name: string;
  email: string;
  level: number;
  referrer_id?: string;
  ncm_key_used: string;
  is_admin: boolean;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface MockNCMKey {
  id: string;
  key: string;
  level: number;
  generated_by_member_id?: string;
  assigned_to_member_id?: string;
  used: boolean;
  created_at: string;
  used_at?: string;
}

export interface MockProduct {
  id: string;
  name: string;
  description: string;
  image_url: string;
  price_eur: number;
  price_btc: number;
  available: boolean;
  created_at: string;
  updated_at: string;
}

export interface MockBitcoinPayment {
  id: string;
  member_id: string;
  product_id: string;
  btc_address: string;
  amount_btc: number;
  status: 'pending' | 'confirmed' | 'failed';
  created_at: string;
  confirmed_at?: string;
  updated_at: string;
}

export interface MockCoupon {
  id: string;
  code: string;
  discount_pct: number;
  assigned_to_member_id?: string;
  used: boolean;
  expires_at?: string;
  created_at: string;
  used_at?: string;
}

export interface MockAdminSetting {
  id: string;
  setting_name: string;
  value: string;
  created_at: string;
  updated_at: string;
}

export interface MockUser {
  id: string;
  email: string;
  password: string; // In real app, this would be hashed
}

// Mock database class
class MockDatabase {
  private members: MockMember[] = [];
  private ncmKeys: MockNCMKey[] = [];
  private products: MockProduct[] = [];
  private bitcoinPayments: MockBitcoinPayment[] = [];
  private coupons: MockCoupon[] = [];
  private adminSettings: MockAdminSetting[] = [];
  private users: MockUser[] = [];
  private currentUser: MockUser | null = null;

  constructor() {
    this.initializeData();
  }

  private initializeData() {
    // Initialize mock users
    this.users = [
      {
        id: 'admin-user-id',
        email: '<EMAIL>',
        password: 'admin123'
      },
      {
        id: 'member-user-id-1',
        email: '<EMAIL>',
        password: 'member123'
      },
      {
        id: 'member-user-id-2',
        email: '<EMAIL>',
        password: 'member123'
      },
      {
        id: 'member-user-id-3',
        email: '<EMAIL>',
        password: 'member123'
      }
    ];

    // Initialize mock members
    this.members = [
      {
        id: 'admin-member-id',
        auth_id: 'admin-user-id',
        name: 'Administrateur IAFUL',
        email: '<EMAIL>',
        level: 0,
        ncm_key_used: 'ADMIN',
        is_admin: true,
        active: true,
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z'
      },
      {
        id: 'member-1',
        auth_id: 'member-user-id-1',
        name: 'Jean Dupont',
        email: '<EMAIL>',
        level: 1,
        ncm_key_used: 'NCM-1-ABC123-XYZ1',
        is_admin: false,
        active: true,
        created_at: '2025-01-15T10:30:00Z',
        updated_at: '2025-01-15T10:30:00Z'
      },
      {
        id: 'member-2',
        auth_id: 'member-user-id-2',
        name: 'Marie Martin',
        email: '<EMAIL>',
        level: 2,
        referrer_id: 'member-1',
        ncm_key_used: 'NCM-2-DEF456-UVW2',
        is_admin: false,
        active: true,
        created_at: '2025-01-16T14:20:00Z',
        updated_at: '2025-01-16T14:20:00Z'
      },
      {
        id: 'member-3',
        auth_id: 'member-user-id-3',
        name: 'Pierre Durand',
        email: '<EMAIL>',
        level: 3,
        referrer_id: 'member-2',
        ncm_key_used: 'NCM-3-GHI789-RST3',
        is_admin: false,
        active: true,
        created_at: '2025-01-17T09:15:00Z',
        updated_at: '2025-01-17T09:15:00Z'
      }
    ];

    // Initialize mock NCM keys
    this.ncmKeys = [
      {
        id: 'key-1',
        key: 'NCM-1-ABC123-XYZ1',
        level: 1,
        assigned_to_member_id: 'member-1',
        used: true,
        created_at: '2025-01-15T10:30:00Z',
        used_at: '2025-01-16T14:20:00Z'
      },
      {
        id: 'key-2',
        key: 'NCM-2-DEF456-UVW2',
        level: 2,
        generated_by_member_id: 'member-1',
        assigned_to_member_id: 'member-2',
        used: true,
        created_at: '2025-01-15T10:30:00Z',
        used_at: '2025-01-17T09:15:00Z'
      },
      {
        id: 'key-3',
        key: 'NCM-3-GHI789-RST3',
        level: 3,
        generated_by_member_id: 'member-2',
        assigned_to_member_id: 'member-3',
        used: true,
        created_at: '2025-01-16T10:30:00Z',
        used_at: '2025-01-18T16:45:00Z'
      },
      {
        id: 'key-4',
        key: 'NCM-1-JKL012-MNO4',
        level: 1,
        generated_by_member_id: 'admin-member-id',
        used: false,
        created_at: '2025-01-18T10:00:00Z'
      },
      {
        id: 'key-5',
        key: 'NCM-1-PQR345-STU5',
        level: 1,
        generated_by_member_id: 'admin-member-id',
        used: false,
        created_at: '2025-01-18T10:00:00Z'
      }
    ];

    // Initialize mock products
    this.products = [
      {
        id: 'product-1',
        name: 'Le Jaune',
        description: 'Un classique d\'exception, fabriqué à partir de fleurs marocaines soigneusement sélectionnées, récoltées à la main, puis séchées lentement et tamisées à froid pour préserver toute la richesse de leurs arômes. Résultat : une couleur jaune éclatante, une texture mousseuse incomparable et une expérience sensorielle unique.',
        image_url: '/Jaune(Filtré).jpg',
        price_eur: 10,
        price_btc: 0.00027,
        available: true,
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z'
      },
      {
        id: 'product-2',
        name: 'Le Mousseux',
        description: 'Issu d\'un tamisage à sec précis et d\'un savoir-faire artisanal, ce hash incarne l\'alliance parfaite entre la tradition marocaine et l\'exigence française. Une texture souple, une montée douce, et un goût qui révèle toute la finesse de sa fabrication.',
        image_url: '/La Mousseux.jpg',
        price_eur: 5,
        price_btc: 0.00013,
        available: true,
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z'
      },
      {
        id: 'product-3',
        name: 'La California',
        description: 'Référence incontournable, la California séduit par son profil aromatique captivant, mêlant notes boisées et fruitées. Chaque effluve évoque la diversité olfactive des vergers californiens, pour une expérience à la fois raffinée et immersive.',
        image_url: '/La Cali.jpg',
        price_eur: 10,
        price_btc: 0.00027,
        available: true,
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z'
      },
      {
        id: 'product-4',
        name: 'L\'Amnesia',
        description: 'Son profil aromatique unique vous emmène dans une expérience de détente profonde tout en stimulant vos sens. Idéale pour se relaxer, elle reste une des variétés préférées des amateurs de weed, pour son équilibre entre puissance et subtilité.',
        image_url: '/L\'Amnesia.jpg',
        price_eur: 8,
        price_btc: 0.00021,
        available: true,
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z'
      }
    ];

    // Initialize mock bitcoin payments
    this.bitcoinPayments = [
      {
        id: 'payment-1',
        member_id: 'member-1',
        product_id: 'product-1', // Le Jaune
        btc_address: '**********************************',
        amount_btc: 0.00027,
        status: 'confirmed',
        created_at: '2025-01-16T10:00:00Z',
        confirmed_at: '2025-01-16T11:30:00Z',
        updated_at: '2025-01-16T11:30:00Z'
      },
      {
        id: 'payment-2',
        member_id: 'member-2',
        product_id: 'product-2', // Le Mousseux
        btc_address: '**********************************',
        amount_btc: 0.00013,
        status: 'pending',
        created_at: '2025-01-17T14:00:00Z',
        updated_at: '2025-01-17T14:00:00Z'
      },
      {
        id: 'payment-3',
        member_id: 'member-3',
        product_id: 'product-3', // La California
        btc_address: '**********************************',
        amount_btc: 0.00027,
        status: 'failed',
        created_at: '2025-01-18T09:00:00Z',
        updated_at: '2025-01-18T12:00:00Z'
      },
      {
        id: 'payment-4',
        member_id: 'member-1',
        product_id: 'product-4', // L'Amnesia
        btc_address: '1C2D3E4F5G6H7I8J9K0L1M2N3O4P5Q6R7S',
        amount_btc: 0.00021,
        status: 'confirmed',
        created_at: '2025-01-19T15:30:00Z',
        confirmed_at: '2025-01-19T16:00:00Z',
        updated_at: '2025-01-19T16:00:00Z'
      }
    ];

    // Initialize mock coupons
    this.coupons = [
      {
        id: 'coupon-1',
        code: 'WELCOME10',
        discount_pct: 10,
        assigned_to_member_id: 'member-1',
        used: false,
        expires_at: '2025-12-31T23:59:59Z',
        created_at: '2025-01-15T10:30:00Z'
      },
      {
        id: 'coupon-2',
        code: 'PREMIUM20',
        discount_pct: 20,
        assigned_to_member_id: 'member-2',
        used: true,
        expires_at: '2025-06-30T23:59:59Z',
        created_at: '2025-01-16T14:20:00Z',
        used_at: '2025-01-17T10:00:00Z'
      },
      {
        id: 'coupon-3',
        code: 'ELITE15',
        discount_pct: 15,
        used: false,
        expires_at: '2025-03-31T23:59:59Z',
        created_at: '2025-01-18T16:45:00Z'
      }
    ];

    // Initialize mock admin settings
    this.adminSettings = [
      {
        id: 'setting-1',
        setting_name: 'bitcoin_payment_enabled',
        value: 'true',
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z'
      },
      {
        id: 'setting-2',
        setting_name: 'initial_ncm_limit',
        value: '10',
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z'
      },
      {
        id: 'setting-3',
        setting_name: 'whatsapp_admin_number',
        value: '+33123456789',
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z'
      },
      {
        id: 'setting-4',
        setting_name: 'admin_email',
        value: '<EMAIL>',
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z'
      },
      {
        id: 'setting-5',
        setting_name: 'registration_enabled',
        value: 'true',
        created_at: '2025-01-01T00:00:00Z',
        updated_at: '2025-01-01T00:00:00Z'
      }
    ];
  }

  // Authentication methods
  async signInWithPassword(email: string, password: string) {
    console.log('Mock signInWithPassword called with:', { email, password });
    console.log('Available users:', this.users.map(u => ({ email: u.email, id: u.id })));

    const user = this.users.find(u => u.email === email && u.password === password);
    if (!user) {
      console.log('No user found with these credentials');
      throw new Error('Invalid credentials');
    }

    console.log('User found:', { id: user.id, email: user.email });
    this.currentUser = user;

    const result = {
      data: {
        user: {
          id: user.id,
          email: user.email
        }
      },
      error: null
    };

    console.log('Returning result:', result);
    return result;
  }

  async signOut() {
    this.currentUser = null;
    return { error: null };
  }

  async getSession() {
    if (!this.currentUser) {
      return { data: { session: null }, error: null };
    }
    return {
      data: {
        session: {
          user: {
            id: this.currentUser.id,
            email: this.currentUser.email
          }
        }
      },
      error: null
    };
  }

  // Member methods
  async getMemberById(id: string): Promise<MockMember | null> {
    return this.members.find(m => m.id === id || m.auth_id === id) || null;
  }

  async getMembers(): Promise<MockMember[]> {
    return [...this.members];
  }

  async createMember(memberData: Partial<MockMember>): Promise<MockMember> {
    const newMember: MockMember = {
      id: `member-${Date.now()}`,
      auth_id: memberData.auth_id || `auth-${Date.now()}`,
      name: memberData.name || '',
      email: memberData.email || '',
      level: memberData.level || 1,
      referrer_id: memberData.referrer_id,
      ncm_key_used: memberData.ncm_key_used || '',
      is_admin: memberData.is_admin || false,
      active: memberData.active !== false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    this.members.push(newMember);
    return newMember;
  }

  // Product methods
  async getProducts(availableOnly: boolean = false): Promise<MockProduct[]> {
    if (availableOnly) {
      return this.products.filter(p => p.available);
    }
    return [...this.products];
  }

  async getProductById(id: string): Promise<MockProduct | null> {
    return this.products.find(p => p.id === id) || null;
  }

  async createProduct(productData: Partial<MockProduct>): Promise<MockProduct> {
    const newProduct: MockProduct = {
      id: `product-${Date.now()}`,
      name: productData.name || '',
      description: productData.description || '',
      image_url: productData.image_url || '',
      price_eur: productData.price_eur || 0,
      price_btc: productData.price_btc || 0,
      available: productData.available !== false,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    this.products.push(newProduct);
    return newProduct;
  }

  // NCM Key methods
  async getNCMKeys(): Promise<MockNCMKey[]> {
    return [...this.ncmKeys];
  }

  async getNCMKeysByMember(memberId: string): Promise<MockNCMKey[]> {
    return this.ncmKeys.filter(k => k.generated_by_member_id === memberId || k.assigned_to_member_id === memberId);
  }

  async createNCMKeys(keysData: Partial<MockNCMKey>[]): Promise<MockNCMKey[]> {
    const newKeys: MockNCMKey[] = keysData.map((keyData, index) => ({
      id: `key-${Date.now()}-${index}`,
      key: keyData.key || generateNCMKey(keyData.level || 1),
      level: keyData.level || 1,
      generated_by_member_id: keyData.generated_by_member_id,
      assigned_to_member_id: keyData.assigned_to_member_id,
      used: keyData.used || false,
      created_at: new Date().toISOString(),
      used_at: keyData.used_at
    }));
    this.ncmKeys.push(...newKeys);
    return newKeys;
  }

  async validateNCMKey(key: string): Promise<MockNCMKey | null> {
    return this.ncmKeys.find(k => k.key === key && !k.used) || null;
  }

  async useNCMKey(key: string, memberId: string): Promise<boolean> {
    const ncmKey = this.ncmKeys.find(k => k.key === key && !k.used);
    if (ncmKey) {
      ncmKey.used = true;
      ncmKey.assigned_to_member_id = memberId;
      ncmKey.used_at = new Date().toISOString();
      return true;
    }
    return false;
  }

  // Bitcoin Payment methods
  async getBitcoinPayments(): Promise<MockBitcoinPayment[]> {
    return [...this.bitcoinPayments];
  }

  async getBitcoinPaymentsByMember(memberId: string): Promise<MockBitcoinPayment[]> {
    return this.bitcoinPayments.filter(p => p.member_id === memberId);
  }

  async createBitcoinPayment(paymentData: Partial<MockBitcoinPayment>): Promise<MockBitcoinPayment> {
    const newPayment: MockBitcoinPayment = {
      id: `payment-${Date.now()}`,
      member_id: paymentData.member_id || '',
      product_id: paymentData.product_id || '',
      btc_address: paymentData.btc_address || '',
      amount_btc: paymentData.amount_btc || 0,
      status: paymentData.status || 'pending',
      created_at: new Date().toISOString(),
      confirmed_at: paymentData.confirmed_at,
      updated_at: new Date().toISOString()
    };
    this.bitcoinPayments.push(newPayment);
    return newPayment;
  }

  // Coupon methods
  async getCoupons(): Promise<MockCoupon[]> {
    return [...this.coupons];
  }

  async getCouponsByMember(memberId: string): Promise<MockCoupon[]> {
    return this.coupons.filter(c => c.assigned_to_member_id === memberId);
  }

  async validateCoupon(code: string): Promise<MockCoupon | null> {
    const coupon = this.coupons.find(c => c.code === code && !c.used);
    if (coupon && coupon.expires_at) {
      const now = new Date();
      const expiresAt = new Date(coupon.expires_at);
      if (now > expiresAt) {
        return null; // Expired
      }
    }
    return coupon || null;
  }

  // Admin Settings methods
  async getAdminSettings(): Promise<MockAdminSetting[]> {
    return [...this.adminSettings];
  }

  async getAdminSetting(settingName: string): Promise<MockAdminSetting | null> {
    return this.adminSettings.find(s => s.setting_name === settingName) || null;
  }

  async updateAdminSetting(settingName: string, value: string): Promise<MockAdminSetting | null> {
    const setting = this.adminSettings.find(s => s.setting_name === settingName);
    if (setting) {
      setting.value = value;
      setting.updated_at = new Date().toISOString();
      return setting;
    }
    return null;
  }

  // Statistics methods for admin dashboard
  async getStats() {
    return {
      totalMembers: this.members.length,
      totalProducts: this.products.length,
      totalSales: this.bitcoinPayments.filter(p => p.status === 'confirmed').length,
      pendingPayments: this.bitcoinPayments.filter(p => p.status === 'pending').length,
      totalNCMKeys: this.ncmKeys.length,
      usedNCMKeys: this.ncmKeys.filter(k => k.used).length,
      availableNCMKeys: this.ncmKeys.filter(k => !k.used).length
    };
  }

  // Utility method to reset data (useful for testing)
  resetData() {
    this.initializeData();
    this.currentUser = null;
  }

  // Method to check if we're using mock auth
  isMockMode(): boolean {
    return true;
  }
}

// Export singleton instance
export const mockDB = new MockDatabase();
