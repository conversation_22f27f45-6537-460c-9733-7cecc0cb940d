// Additional test data scenarios for IAFUL
// This file contains various test scenarios to thoroughly test the application

import { mockDB } from './mockAuth';

export interface TestScenario {
  name: string;
  description: string;
  setup: () => Promise<void>;
}

// Test scenario: Large member hierarchy
export const largeHierarchyScenario: TestScenario = {
  name: 'Large Member Hierarchy',
  description: 'Creates a complex member hierarchy with multiple levels and branches',
  setup: async () => {
    // Reset to clean state
    mockDB.resetData();

    // Create additional members for testing hierarchy
    const members = [
      {
        id: 'member-4',
        auth_id: 'member-user-id-4',
        name: '<PERSON>',
        email: '<EMAIL>',
        level: 2,
        referrer_id: 'member-1',
        ncm_key_used: 'NCM-2-ABC789-DEF4',
        is_admin: false,
        active: true,
        created_at: '2025-01-19T10:00:00Z',
        updated_at: '2025-01-19T10:00:00Z'
      },
      {
        id: 'member-5',
        auth_id: 'member-user-id-5',
        name: '<PERSON>',
        email: '<EMAIL>',
        level: 3,
        referrer_id: 'member-4',
        ncm_key_used: 'NCM-3-GHI012-JKL5',
        is_admin: false,
        active: true,
        created_at: '2025-01-20T14:30:00Z',
        updated_at: '2025-01-20T14:30:00Z'
      },
      {
        id: 'member-6',
        auth_id: 'member-user-id-6',
        name: 'Emma Dubois',
        email: '<EMAIL>',
        level: 4,
        referrer_id: 'member-5',
        ncm_key_used: 'NCM-4-MNO345-PQR6',
        is_admin: false,
        active: true,
        created_at: '2025-01-21T09:15:00Z',
        updated_at: '2025-01-21T09:15:00Z'
      }
    ];

    for (const member of members) {
      await mockDB.createMember(member);
    }

    console.log('Large hierarchy scenario setup complete');
  }
};

// Test scenario: High sales volume
export const highSalesVolumeScenario: TestScenario = {
  name: 'High Sales Volume',
  description: 'Creates multiple orders and payments to test sales functionality',
  setup: async () => {
    // Create additional payments
    const payments = [
      {
        member_id: 'member-1',
        product_id: 'product-2', // Le Mousseux
        btc_address: '1A2B3C4D5E6F7G8H9I0J1K2L3M4N5O6P7Q',
        amount_btc: 0.00013,
        status: 'confirmed' as const,
        created_at: '2025-01-19T10:00:00Z',
        confirmed_at: '2025-01-19T11:00:00Z',
        updated_at: '2025-01-19T11:00:00Z'
      },
      {
        member_id: 'member-2',
        product_id: 'product-3', // La California
        btc_address: '1B2C3D4E5F6G7H8I9J0K1L2M3N4O5P6Q7R',
        amount_btc: 0.00027,
        status: 'confirmed' as const,
        created_at: '2025-01-20T15:30:00Z',
        confirmed_at: '2025-01-20T16:00:00Z',
        updated_at: '2025-01-20T16:00:00Z'
      },
      {
        member_id: 'member-3',
        product_id: 'product-4', // L'Amnesia
        btc_address: '1C2D3E4F5G6H7I8J9K0L1M2N3O4P5Q6R7S',
        amount_btc: 0.00021,
        status: 'pending' as const,
        created_at: '2025-01-21T12:00:00Z',
        updated_at: '2025-01-21T12:00:00Z'
      },
      {
        member_id: 'member-1',
        product_id: 'product-1', // Le Jaune
        btc_address: '1D2E3F4G5H6I7J8K9L0M1N2O3P4Q5R6S7T',
        amount_btc: 0.00027,
        status: 'failed' as const,
        created_at: '2025-01-22T08:45:00Z',
        updated_at: '2025-01-22T10:00:00Z'
      }
    ];

    for (const payment of payments) {
      await mockDB.createBitcoinPayment(payment);
    }

    console.log('High sales volume scenario setup complete');
  }
};

// Test scenario: NCM key management
export const ncmKeyManagementScenario: TestScenario = {
  name: 'NCM Key Management',
  description: 'Creates various NCM keys for testing key generation and management',
  setup: async () => {
    // Create additional NCM keys
    const ncmKeys = [
      {
        key: 'NCM-1-TEST001-AAA1',
        level: 1,
        generated_by_member_id: 'admin-member-id',
        used: false,
        created_at: '2025-01-22T10:00:00Z'
      },
      {
        key: 'NCM-2-TEST002-BBB2',
        level: 2,
        generated_by_member_id: 'member-1',
        used: false,
        created_at: '2025-01-22T10:05:00Z'
      },
      {
        key: 'NCM-3-TEST003-CCC3',
        level: 3,
        generated_by_member_id: 'member-2',
        used: false,
        created_at: '2025-01-22T10:10:00Z'
      },
      {
        key: 'NCM-1-USED001-DDD4',
        level: 1,
        generated_by_member_id: 'admin-member-id',
        assigned_to_member_id: 'member-1',
        used: true,
        created_at: '2025-01-20T10:00:00Z',
        used_at: '2025-01-21T14:30:00Z'
      }
    ];

    await mockDB.createNCMKeys(ncmKeys);

    console.log('NCM key management scenario setup complete');
  }
};

// Test scenario: Coupon system
export const couponSystemScenario: TestScenario = {
  name: 'Coupon System',
  description: 'Creates various coupons for testing discount functionality',
  setup: async () => {
    // Additional coupons are already created in the base mock data
    // This scenario could add more complex coupon scenarios
    console.log('Coupon system scenario setup complete');
  }
};

// Export all scenarios
export const testScenarios: TestScenario[] = [
  largeHierarchyScenario,
  highSalesVolumeScenario,
  ncmKeyManagementScenario,
  couponSystemScenario
];

// Utility function to run all test scenarios
export async function setupAllTestScenarios() {
  console.log('Setting up all test scenarios...');
  
  for (const scenario of testScenarios) {
    console.log(`Setting up: ${scenario.name}`);
    await scenario.setup();
  }
  
  console.log('All test scenarios setup complete!');
}

// Utility function to run a specific scenario
export async function setupTestScenario(scenarioName: string) {
  const scenario = testScenarios.find(s => s.name === scenarioName);
  if (scenario) {
    console.log(`Setting up: ${scenario.name}`);
    await scenario.setup();
    console.log(`${scenario.name} setup complete!`);
  } else {
    console.error(`Scenario "${scenarioName}" not found`);
  }
}
