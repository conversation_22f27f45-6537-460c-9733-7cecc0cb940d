import Link from 'next/link';
import { useContext } from 'react';
import { useRouter } from 'next/router';
import { AuthContext, supabase } from '../pages/_app';

type NavbarProps = {
  isAdmin?: boolean;
};

export default function Navbar({ isAdmin = false }: NavbarProps) {
  const router = useRouter();
  const { user } = useContext(AuthContext);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/');
  };

  return (
    <header className="fixed w-full bg-black-deep z-50 border-b border-gold-luxurious/30">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <div className="w-1/3">
          {user ? (
            <Link href={isAdmin ? '/admin' : '/profile'} className="text-gold-luxurious hover:text-white-off transition-colors">
              {isAdmin ? 'Dashboard' : 'Mon Profil'}
            </Link>
          ) : (
            <Link href="/" className="text-gold-luxurious hover:text-white-off transition-colors">
              Accueil
            </Link>
          )}
        </div>
        
        <div className="w-1/3 flex justify-center">
          <Link href={user ? (isAdmin ? '/admin' : '/profile') : '/'} className="flex items-center">
            <span className="text-2xl font-playfair font-bold text-gold-luxurious">
              {isAdmin ? 'IAFUL Admin' : 'IAFUL'}
            </span>
          </Link>
        </div>
        
        <div className="w-1/3 flex justify-end space-x-4">
          {user ? (
            <>
              {!isAdmin && (
                <Link href="/products" className={`${router.pathname === '/products' || router.pathname.startsWith('/products/') ? 'text-gold-luxurious' : 'text-white-off hover:text-gold-luxurious transition-colors'}`}>
                  Produits
                </Link>
              )}
              <button
                onClick={handleSignOut}
                className="text-white-off hover:text-gold-luxurious transition-colors"
              >
                Déconnexion
              </button>
            </>
          ) : (
            <>
              <Link href="/login" className={`${router.pathname === '/login' ? 'text-gold-luxurious' : 'text-white-off hover:text-gold-luxurious transition-colors'}`}>
                Connexion
              </Link>
              <Link href="/register" className="btn">
                Inscription
              </Link>
            </>
          )}
        </div>
      </div>
    </header>
  );
}
