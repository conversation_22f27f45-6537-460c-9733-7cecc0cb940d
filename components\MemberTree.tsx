import { useState, useEffect, useCallback } from 'react';
import React<PERSON>low, {
  Node,
  Edge,
  Controls,
  Background,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  Connection,
  MarkerType,
} from 'reactflow';
import 'reactflow/dist/style.css';

// Custom node component for members
const MemberNode = ({ data }: { data: any }) => {
  if (!data) {
    return (
      <div className="p-2 rounded-md shadow-md" style={{ backgroundColor: '#888888', color: '#fff', minWidth: '100px' }}>
        <div className="font-bold text-center"><PERSON><PERSON><PERSON> manquantes</div>
      </div>
    );
  }

  const levelColors = {
    0: '#CFAF5A', // Admin - Gold
    1: '#E5C675', // Level 1
    2: '#F0D890', // Level 2
    3: '#F5E2A5', // Level 3
    4: '#F8E9B8', // Level 4
    5: '#FAF0CB', // Level 5
    6: '#FDF7DE', // Level 6
  };

  const level = data.level !== undefined ? data.level : 0;
  const bgColor = levelColors[level as keyof typeof levelColors] || '#CFAF5A';

  // Get NCM code generation capacity based on level
  const getNcmCapacity = (level: number) => {
    switch (level) {
      case 0: return 10; // Admin generates 10 NCM codes
      case 1: return 10; // Level 1 generates 10 NCM codes
      case 2: return 8;  // Level 2 generates 8 NCM codes
      case 3: return 6;  // Level 3 generates 6 NCM codes
      case 4: return 4;  // Level 4 generates 4 NCM codes
      case 5: return 2;  // Level 5 generates 2 NCM codes
      case 6: return 0;  // Level 6 doesn't generate NCM codes
      default: return 0;
    }
  };

  const ncmCapacity = getNcmCapacity(level);
  const usedNcm = data.usedKeys !== undefined ? data.usedKeys : 0;
  const totalNcm = data.totalKeys !== undefined ? data.totalKeys : ncmCapacity;
  const name = data.name || 'Membre';

  // Special styling for "more" indicator nodes
  if (data.isMoreIndicator) {
    return (
      <div className="p-2 rounded-md shadow-md" style={{ backgroundColor: '#888888', color: '#fff', minWidth: '100px' }}>
        <div className="font-bold text-center">{data.name}</div>
      </div>
    );
  }

  return (
    <div className="p-3 rounded-md shadow-md" style={{ backgroundColor: bgColor, color: '#000', minWidth: '150px' }}>
      <div className="font-bold mb-1">{name}</div>
      <div className="text-xs">Niveau: {level === 0 ? 'Admin' : level}</div>
      {level < 6 && (
        <div className="text-xs mt-1">
          NCM: {usedNcm}/{totalNcm}
        </div>
      )}
      {level === 6 && (
        <div className="text-xs mt-1">
          Niveau maximum
        </div>
      )}
    </div>
  );
};

// Node types definition
const nodeTypes = {
  memberNode: MemberNode,
};

type MemberTreeProps = {
  rootMember: any;
  members: any[];
  compact?: boolean;
};

export default function MemberTree({ rootMember, members, compact = false }: MemberTreeProps) {
  const [nodes, setNodes, onNodesChange] = useNodesState([]);
  const [edges, setEdges, onEdgesChange] = useEdgesState([]);
  const [loading, setLoading] = useState(true);

  // Function to build the tree structure
  const buildTree = useCallback(() => {
    if (!rootMember || !members) {
      setNodes([]);
      setEdges([]);
      return;
    }

    const newNodes: Node[] = [];
    const newEdges: Edge[] = [];

    // Get NCM code generation capacity based on level
    const getNcmCapacity = (level: number) => {
      switch (level) {
        case 0: return 10; // Admin generates 10 NCM codes
        case 1: return 10; // Level 1 generates 10 NCM codes
        case 2: return 8;  // Level 2 generates 8 NCM codes
        case 3: return 6;  // Level 3 generates 6 NCM codes
        case 4: return 4;  // Level 4 generates 4 NCM codes
        case 5: return 2;  // Level 5 generates 2 NCM codes
        case 6: return 0;  // Level 6 doesn't generate NCM codes
        default: return 0;
      }
    };

    // Helper function to recursively build the tree
    const buildTreeRecursive = (member: any, x: number, y: number, level: number = 0, maxDepth: number = 6) => {
      if (!member || !member.id) {
        return; // Skip invalid members
      }

      // Create node for this member
      const nodeId = `member-${member.id}`;

      // Find direct referrals for this member
      const referrals = members.filter(m => m.referrer_id === member.id);

      // Calculate NCM keys usage
      const totalKeys = getNcmCapacity(member.level);
      const usedKeys = referrals.length; // Used keys is the number of direct referrals

      // Add node
      newNodes.push({
        id: nodeId,
        type: 'memberNode',
        position: { x, y },
        data: {
          ...member,
          usedKeys,
          totalKeys,
          level: member.level, // Ensure level is passed correctly
        },
      });

      // If compact mode, limit the depth
      if (compact && level >= 1) {
        return;
      }

      // If we've reached the maximum depth or this is level 6 (which doesn't have children), stop recursion
      if (level >= maxDepth || member.level >= 6) {
        return;
      }

      // Add children if there are any
      if (referrals.length > 0) {
        // Limit the number of children to display to avoid performance issues
        const maxChildrenToShow = compact ? 3 : 10;
        const childrenToShow = referrals.slice(0, maxChildrenToShow);

        const childWidth = 180;
        const startX = x - ((childrenToShow.length - 1) * childWidth) / 2;

        childrenToShow.forEach((referral, index) => {
          if (!referral || !referral.id) {
            return; // Skip invalid referrals
          }

          const childX = startX + index * childWidth;
          const childY = y + 100;

          // Add edge
          newEdges.push({
            id: `edge-${member.id}-${referral.id}`,
            source: nodeId,
            target: `member-${referral.id}`,
            type: 'smoothstep',
            markerEnd: {
              type: MarkerType.ArrowClosed,
            },
          });

          // Recursively build tree for this child
          buildTreeRecursive(referral, childX, childY, level + 1, maxDepth);
        });

        // If there are more children than we're showing, add an indicator
        if (referrals.length > maxChildrenToShow) {
          const moreCount = referrals.length - maxChildrenToShow;
          const moreNodeId = `more-${member.id}`;
          const moreX = startX + childrenToShow.length * childWidth;
          const moreY = y + 100;

          // Add a special node to indicate there are more children
          newNodes.push({
            id: moreNodeId,
            type: 'memberNode',
            position: { x: moreX, y: moreY },
            data: {
              name: `+ ${moreCount} autres`,
              level: member.level + 1,
              isMoreIndicator: true,
            },
          });

          // Add edge to the "more" node
          newEdges.push({
            id: `edge-${member.id}-more`,
            source: nodeId,
            target: moreNodeId,
            type: 'smoothstep',
            markerEnd: {
              type: MarkerType.ArrowClosed,
            },
          });
        }
      }
    };

    // Determine the maximum depth to show based on compact mode
    const maxDepth = compact ? 1 : 3; // Limit to 3 levels deep for performance in full mode

    // Start building from root member (admin)
    buildTreeRecursive(rootMember, 0, 0, 0, maxDepth);

    setNodes(newNodes);
    setEdges(newEdges);
    setLoading(false);
  }, [rootMember, members, compact, setNodes, setEdges]);

  // Build tree when data changes
  useEffect(() => {
    buildTree();
  }, [buildTree]);

  // Handle edge connections
  const onConnect = useCallback(
    (connection: Connection) => {
      setEdges((eds) => addEdge(connection, eds));
    },
    [setEdges]
  );

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gold-luxurious">Chargement...</div>
      </div>
    );
  }

  return (
    <div style={{ height: compact ? 300 : 600 }} className="border border-gold-luxurious/30 rounded-md overflow-hidden">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        nodeTypes={nodeTypes}
        fitView
      >
        <Controls />
        <Background color="#1E1E1E" gap={16} />
        {!compact && <MiniMap />}
      </ReactFlow>
    </div>
  );
}
