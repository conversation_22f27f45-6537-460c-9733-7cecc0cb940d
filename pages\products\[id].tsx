import Head from 'next/head';
import Link from 'next/link';
import Image from 'next/image';
import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import { AuthContext, supabase } from '../_app';
import Layout from '../../components/Layout';

export default function ProductDetail() {
  const router = useRouter();
  const { id } = router.query;
  const { user, session } = useContext(AuthContext);
  const [loading, setLoading] = useState(true);
  const [product, setProduct] = useState<any>(null);
  const [paymentStep, setPaymentStep] = useState(0);
  const [btcAddress, setBtcAddress] = useState('');
  const [paymentStatus, setPaymentStatus] = useState('pending');

  useEffect(() => {
    // Check if user is authenticated
    if (!user) {
      router.push('/login');
      return;
    }

    // Fetch product data
    const fetchProduct = async () => {
      if (!id) return;

      try {
        setLoading(true);

        // Use Supabase to fetch product
        const { data, error } = await supabase
          .from('products')
          .select('*')
          .eq('id', id)
          .eq('available', true)
          .single();

        if (error || !data) {
          router.push('/products');
          return;
        }

        setProduct(data);
      } catch (error) {
        console.error('Error fetching product:', error);
        router.push('/products');
      } finally {
        setLoading(false);
      }
    };

    fetchProduct();
  }, [user, router, id]);

  const handleSignOut = async () => {
    await supabase.auth.signOut();
    router.push('/');
  };

  const handlePurchase = () => {
    // In a real implementation, you would integrate with BTCPay Server
    // For now, we'll simulate the payment process
    setPaymentStep(1);

    // Generate a fake BTC address
    setBtcAddress('******************************************');

    // Simulate payment confirmation after 5 seconds
    setTimeout(() => {
      setPaymentStatus('confirmed');
      setPaymentStep(2);
    }, 5000);
  };

  const handleContactAdmin = () => {
    // In a real implementation, you would use the WhatsApp API
    // For now, we'll just open a WhatsApp link
    window.open('https://wa.me/1234567890', '_blank');
  };

  if (loading) {
    return (
      <Layout title="Chargement... | IAFUL">
        <div className="flex items-center justify-center h-64">
          <div className="text-gold-luxurious text-xl">Chargement...</div>
        </div>
      </Layout>
    );
  }

  if (!product) {
    return (
      <Layout title="Produit non trouvé | IAFUL">
        <div className="flex items-center justify-center h-64">
          <div className="text-gold-luxurious text-xl">Produit non trouvé</div>
        </div>
      </Layout>
    );
  }

  return (
    <Layout title={`${product.name} | IAFUL`}>
      <div className="container mx-auto px-4">
        <div className="max-w-6xl mx-auto">
          <nav className="flex py-4 mb-6" aria-label="Breadcrumb">
            <ol className="inline-flex items-center space-x-1 md:space-x-3">
              <li className="inline-flex items-center">
                <Link href="/" className="text-white-off/70 hover:text-gold-luxurious inline-flex items-center">
                  <svg className="w-3 h-3 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"></path>
                  </svg>
                  Accueil
                </Link>
              </li>
              <li>
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-white-off/50 mx-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
                  </svg>
                  <Link href="/products" className="text-white-off/70 hover:text-gold-luxurious ml-1 md:ml-2">
                    Produits
                  </Link>
                </div>
              </li>
              <li aria-current="page">
                <div className="flex items-center">
                  <svg className="w-3 h-3 text-white-off/50 mx-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd"></path>
                  </svg>
                  <span className="text-gold-luxurious ml-1 md:ml-2 font-medium">{product.name}</span>
                </div>
              </li>
            </ol>
          </nav>

          <div className="bg-gray-anthracite/80 backdrop-blur-sm rounded-lg shadow-xl overflow-hidden mb-12">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-0">
              {/* Product Image with Gallery Feel */}
              <div className="relative h-96 md:h-[600px] overflow-hidden border-b md:border-b-0 md:border-r border-gold-luxurious/20">
                <div className="absolute inset-0 bg-black-deep/30 z-10"></div>
                <Image
                  src={product.image_url}
                  alt={product.name}
                  fill
                  priority
                  className="object-cover transition-transform duration-700 hover:scale-105"
                  style={{ objectFit: 'cover' }}
                />
                <div className="absolute top-4 right-4 z-20">
                  <span className="bg-gold-luxurious text-black-deep text-xs font-bold px-3 py-1 rounded-full">
                    Premium
                  </span>
                </div>
              </div>

              {/* Product Details */}
              <div className="p-8 flex flex-col h-full justify-between">
                <div>
                  <h1 className="text-4xl font-playfair font-bold text-gold-luxurious mb-4 tracking-wide">
                    {product.name}
                  </h1>

                  <div className="flex items-center mb-6">
                    <div className="flex text-gold-luxurious">
                      {[...Array(5)].map((_, i) => (
                        <svg key={i} className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                      ))}
                    </div>
                    <span className="text-white-off/70 ml-2 text-sm">(12 avis)</span>
                  </div>

                  <div className="prose prose-invert max-w-none mb-8">
                    <p className="text-white-off text-lg leading-relaxed">
                      {product.description}
                    </p>
                  </div>

                  <div className="mb-8">
                    <div className="flex flex-col space-y-2">
                      <div className="flex justify-between items-center py-2 border-b border-gold-luxurious/20">
                        <span className="text-white-off/70">Disponibilité:</span>
                        <span className="text-gold-luxurious font-medium">En stock</span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gold-luxurious/20">
                        <span className="text-white-off/70">Catégorie:</span>
                        <span className="text-white-off">Premium</span>
                      </div>
                      <div className="flex justify-between items-center py-2 border-b border-gold-luxurious/20">
                        <span className="text-white-off/70">Livraison:</span>
                        <span className="text-white-off">24-48h</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <div className="flex items-center justify-between mb-6">
                    <div className="flex flex-col">
                      <span className="text-white-off/70 text-sm">Prix</span>
                      <div className="flex items-center">
                        <span className="text-gold-luxurious font-bold text-3xl">{product.price_eur} €</span>
                        <span className="text-white-off/70 text-sm ml-2">/ {product.price_btc} BTC</span>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className="bg-gray-anthracite p-2 rounded-full border border-gold-luxurious/30 hover:border-gold-luxurious transition-colors">
                        <svg className="w-6 h-6 text-gold-luxurious" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                        </svg>
                      </button>
                      <button className="bg-gray-anthracite p-2 rounded-full border border-gold-luxurious/30 hover:border-gold-luxurious transition-colors">
                        <svg className="w-6 h-6 text-gold-luxurious" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.368 2.684 3 3 0 00-5.368-2.684z"></path>
                        </svg>
                      </button>
                    </div>
                  </div>

                  {paymentStep === 0 && (
                    <div className="flex flex-col space-y-4">
                      <button
                        onClick={handlePurchase}
                        className="btn w-full py-4 text-lg font-medium flex items-center justify-center space-x-2 transition-transform hover:scale-[1.02]"
                      >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path d="M3 1a1 1 0 000 2h1.22l.305 1.222a.997.997 0 00.01.042l1.358 5.43-.893.892C3.74 11.846 4.632 14 6.414 14H15a1 1 0 000-2H6.414l1-1H14a1 1 0 00.894-.553l3-6A1 1 0 0017 3H6.28l-.31-1.243A1 1 0 005 1H3zM16 16.5a1.5 1.5 0 11-3 0 1.5 1.5 0 013 0zM6.5 18a1.5 1.5 0 100-3 1.5 1.5 0 000 3z"></path>
                        </svg>
                        <span>Acheter maintenant</span>
                      </button>
                      <button
                        onClick={handleContactAdmin}
                        className="w-full py-3 border border-gold-luxurious text-gold-luxurious rounded-button hover:bg-gold-luxurious/10 transition-colors flex items-center justify-center space-x-2"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"></path>
                        </svg>
                        <span>Contacter l'administrateur</span>
                      </button>
                    </div>
                  )}

                  {paymentStep === 1 && (
                    <div className="card border border-gold-luxurious/20 rounded-lg">
                      <h3 className="text-xl font-playfair text-gold-luxurious mb-4 flex items-center">
                        <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path d="M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"></path>
                          <path fillRule="evenodd" d="M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z" clipRule="evenodd"></path>
                        </svg>
                        Paiement en Bitcoin
                      </h3>
                      <p className="text-white-off mb-4">
                        Veuillez envoyer exactement <span className="text-gold-luxurious font-bold">{product.price_btc} BTC</span> à l'adresse suivante:
                      </p>
                      <div className="bg-black-deep p-4 rounded-md mb-4 break-all border border-gold-luxurious/20">
                        <p className="text-white-off font-mono">{btcAddress}</p>
                      </div>
                      <div className="flex justify-between items-center">
                        <button
                          onClick={() => navigator.clipboard.writeText(btcAddress)}
                          className="text-gold-luxurious hover:underline flex items-center"
                        >
                          <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"></path>
                            <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"></path>
                          </svg>
                          Copier l'adresse
                        </button>
                        <div className="text-white-off/70 flex items-center">
                          <div className="w-2 h-2 bg-yellow-500 rounded-full mr-2 animate-pulse"></div>
                          Statut: <span className="text-yellow-500 ml-1">En attente</span>
                        </div>
                      </div>
                    </div>
                  )}

                  {paymentStep === 2 && (
                    <div className="card border border-green-500/20 rounded-lg bg-gray-anthracite/80">
                      <div className="flex items-center justify-center mb-4">
                        <div className="rounded-full bg-green-500/20 p-3">
                          <svg className="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                          </svg>
                        </div>
                      </div>
                      <h3 className="text-xl font-playfair text-gold-luxurious mb-4 text-center">
                        Paiement confirmé
                      </h3>
                      <p className="text-white-off mb-4 text-center">
                        Votre paiement a été confirmé. Merci pour votre achat!
                      </p>
                      <p className="text-white-off mb-6 text-center">
                        Un administrateur vous contactera prochainement pour organiser la livraison.
                      </p>
                      <button
                        onClick={() => router.push('/products')}
                        className="btn w-full py-3 flex items-center justify-center"
                      >
                        <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
                          <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd"></path>
                        </svg>
                        Continuer mes achats
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Related Products Section */}
          <RelatedProducts currentProductId={product.id} />
        </div>
      </div>
    </Layout>
  );
}

// Related Products Component
function RelatedProducts({ currentProductId }: { currentProductId: string }) {
  const [relatedProducts, setRelatedProducts] = useState<any[]>([]);

  useEffect(() => {
    const fetchRelatedProducts = async () => {
      try {
        const { data } = await supabase
          .from('products')
          .select('*')
          .eq('available', true)
          .neq('id', currentProductId)
          .limit(3);
        setRelatedProducts(data || []);
      } catch (error) {
        console.error('Error fetching related products:', error);
      }
    };

    fetchRelatedProducts();
  }, [currentProductId]);

  return (
    <div className="mb-12">
      <h2 className="text-2xl font-playfair font-bold text-gold-luxurious mb-6">
        Produits similaires
      </h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {relatedProducts.map((relatedProduct) => (
          <Link href={`/products/${relatedProduct.id}`} key={relatedProduct.id} className="group">
            <div className="bg-gray-anthracite/60 rounded-lg overflow-hidden shadow-lg hover:shadow-gold-luxurious/20 transition-all duration-300 hover:translate-y-[-5px]">
              <div className="relative h-48 overflow-hidden">
                <Image
                  src={relatedProduct.image_url}
                  alt={relatedProduct.name}
                  fill
                  className="object-cover transition-transform duration-500 group-hover:scale-110"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/placeholder-product.jpg';
                  }}
                />
              </div>
              <div className="p-4">
                <h3 className="text-lg font-playfair text-gold-luxurious group-hover:text-gold-luxurious/80 transition-colors mb-2">
                  {relatedProduct.name}
                </h3>
                <p className="text-white-off/70 text-sm mb-3 line-clamp-2">
                  {relatedProduct.description.substring(0, 80)}...
                </p>
                <div className="flex justify-between items-center">
                  <span className="text-white-off font-bold text-lg">{relatedProduct.price_eur} €</span>
                  <span className="text-xs text-white-off/70 px-2 py-1 bg-black-deep/40 rounded-full">Premium</span>
                </div>
              </div>
            </div>
          </Link>
        ))}
      </div>
    </div>
  );
}
