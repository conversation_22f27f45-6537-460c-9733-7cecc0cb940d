import Head from 'next/head';
import Image from 'next/image';
import Link from 'next/link';
import dynamic from 'next/dynamic';
import ScrollAnimation from '../components/ScrollAnimation';
import ScrollToTop from '../components/ScrollToTop';

// Import the CSS-based LogoAnimation component with dynamic loading (no SSR)
const LogoAnimationCSS = dynamic(() => import('../components/LogoAnimationCSS'), {
  ssr: false,
});

export default function Home() {
  return (
    <div className="min-h-screen bg-black-deep">
      <Head>
        <title>IAFUL - Le Club Privé</title>
      </Head>

      {/* Scroll to top button */}
      <ScrollToTop />

      <header className="fixed w-full bg-black-deep z-50 border-b border-gold-luxurious/30">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <div className="w-1/3">
            <Link href="/" className="text-gold-luxurious hover:text-white-off transition-colors">
              Accueil
            </Link>
          </div>
          <div className="w-1/3 flex justify-center">
            <Link href="/" className="flex items-center">
              <span className="text-2xl font-playfair font-bold text-gold-luxurious">IAFUL</span>
            </Link>
          </div>
          <div className="w-1/3 flex justify-end space-x-4">
            <Link href="/login" className="btn">
              Connexion
            </Link>
            <Link href="/register" className="btn">
              Inscription
            </Link>
          </div>
        </div>
      </header>

      <main>
        {/* Hero Section */}
        <section className="relative h-screen flex items-center justify-center overflow-hidden">
          <div className="absolute inset-0 z-0">
            <div className="absolute inset-0 bg-black-deep/70 z-10"></div>
            <div
              className="absolute inset-0 bg-cover bg-center bg-no-repeat z-0"
              style={{ backgroundImage: 'url(/background.jpg)' }}
            ></div>
          </div>

          {/* CSS Logo Animation */}
          <div className="absolute inset-0 z-10 opacity-80">
            <LogoAnimationCSS />
          </div>

          <div className="container mx-auto px-4 z-20 text-center relative">
            <div className="mb-8 flex justify-center">
              <div
                className="relative w-40 h-40 md:w-56 md:h-56 animate-float cursor-pointer"
                onClick={() => {
                  const logoElement = document.getElementById('logo-image');
                  if (logoElement) {
                    logoElement.classList.add('animate-spin-slow');
                    setTimeout(() => {
                      logoElement.classList.remove('animate-spin-slow');
                    }, 2000);
                  }
                }}
              >
                <Image
                  id="logo-image"
                  src="/logo.jpg"
                  alt="IAFUL Logo"
                  fill
                  className="object-contain rounded-full border-4 border-gold-luxurious shadow-lg shadow-gold-luxurious/30 transition-all duration-300 hover:shadow-xl hover:shadow-gold-luxurious/50"
                  priority
                />
              </div>
            </div>
            <h1 className="text-5xl md:text-7xl font-playfair font-bold text-gold-luxurious mb-6">
              IAFUL
            </h1>
            <p className="text-xl md:text-2xl text-white-off mb-8 max-w-3xl mx-auto">
              Le Club Privé Réservé à une Élite Sélectionnée
            </p>
            <Link href="/register" className="btn text-lg px-8 py-3 hover:scale-105 transition-transform">
              Rejoindre le Club
            </Link>
          </div>
        </section>

        {/* About Section */}
        <section className="py-20 bg-gray-anthracite relative overflow-hidden">
          {/* Background decorative elements */}
          <div className="absolute top-0 right-0 w-64 h-64 bg-gold-luxurious/5 rounded-full -translate-y-1/2 translate-x-1/2"></div>
          <div className="absolute bottom-0 left-0 w-96 h-96 bg-gold-luxurious/5 rounded-full translate-y-1/2 -translate-x-1/2"></div>

          <div className="container mx-auto px-4 relative z-10">
            <ScrollAnimation animation="fade-up" className="mb-12">
              <h2 className="text-4xl font-playfair font-bold text-gold-luxurious text-center">
                Un Club d'Exception
              </h2>
            </ScrollAnimation>

            <div className="max-w-4xl mx-auto">
              <ScrollAnimation animation="fade-left" delay={0.2} className="mb-6">
                <p className="text-lg text-white-off">
                  IAFUL est bien plus qu'un simple club privé, c'est une expérience exclusive, un réseau d'exception réservé uniquement à ceux qui en ont l'accès.
                </p>
              </ScrollAnimation>

              <ScrollAnimation animation="fade-right" delay={0.4} className="mb-6">
                <p className="text-lg text-white-off">
                  Rejoindre IAFUL, c'est entrer dans un cercle fermé, où chaque membre bénéficie de privilèges uniques et d'un environnement sécurisé et confidentiel.
                </p>
              </ScrollAnimation>

              <ScrollAnimation animation="fade-up" delay={0.6} className="mb-6">
                <p className="text-lg text-white-off">
                  L'accès à notre communauté est strictement limité et repose sur un système de parrainage rigoureux.
                </p>
              </ScrollAnimation>
            </div>
          </div>
        </section>

        {/* How to Join Section */}
        <section className="py-20 bg-black-deep relative">
          {/* Decorative line */}
          <div className="absolute left-1/2 top-0 bottom-0 w-px bg-gradient-to-b from-transparent via-gold-luxurious/30 to-transparent"></div>

          <div className="container mx-auto px-4 relative z-10">
            <ScrollAnimation animation="fade-up" className="mb-12">
              <h2 className="text-4xl font-playfair font-bold text-gold-luxurious text-center">
                Comment devenir membre ?
              </h2>
            </ScrollAnimation>

            <div className="max-w-4xl mx-auto">
              <ScrollAnimation animation="fade-up" delay={0.2} className="mb-8">
                <p className="text-lg text-white-off text-center">
                  L'entrée dans IAFUL n'est pas libre : elle se mérite.
                  Seul un membre existant peut vous ouvrir les portes du club en vous parrainant personnellement.
                </p>
              </ScrollAnimation>

              <ScrollAnimation animation="fade-up" delay={0.3} className="mb-6">
                <h3 className="text-2xl font-playfair text-gold-luxurious">
                  Le processus d'adhésion
                </h3>
              </ScrollAnimation>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                <ScrollAnimation animation="fade-right" delay={0.4}>
                  <div className="card hover:shadow-lg hover:shadow-gold-luxurious/10 transition-shadow duration-300 transform hover:-translate-y-1">
                    <div className="text-gold-luxurious text-4xl font-bold mb-4">1</div>
                    <h4 className="text-xl font-playfair text-gold-luxurious mb-2">Trouver un parrain</h4>
                    <p className="text-white-off">Seuls les membres IAFUL peuvent inviter une nouvelle personne.</p>
                  </div>
                </ScrollAnimation>

                <ScrollAnimation animation="fade-left" delay={0.5}>
                  <div className="card hover:shadow-lg hover:shadow-gold-luxurious/10 transition-shadow duration-300 transform hover:-translate-y-1">
                    <div className="text-gold-luxurious text-4xl font-bold mb-4">2</div>
                    <h4 className="text-xl font-playfair text-gold-luxurious mb-2">Recevoir votre NCM</h4>
                    <p className="text-white-off">Ce code unique est votre clé d'entrée pour accéder au club et à ses services.</p>
                  </div>
                </ScrollAnimation>

                <ScrollAnimation animation="fade-right" delay={0.6}>
                  <div className="card hover:shadow-lg hover:shadow-gold-luxurious/10 transition-shadow duration-300 transform hover:-translate-y-1">
                    <div className="text-gold-luxurious text-4xl font-bold mb-4">3</div>
                    <h4 className="text-xl font-playfair text-gold-luxurious mb-2">S'inscrire avec son NCM</h4>
                    <p className="text-white-off">Sans ce numéro, aucune inscription n'est possible.</p>
                  </div>
                </ScrollAnimation>

                <ScrollAnimation animation="fade-left" delay={0.7}>
                  <div className="card hover:shadow-lg hover:shadow-gold-luxurious/10 transition-shadow duration-300 transform hover:-translate-y-1">
                    <div className="text-gold-luxurious text-4xl font-bold mb-4">4</div>
                    <h4 className="text-xl font-playfair text-gold-luxurious mb-2">Accéder aux privilèges</h4>
                    <p className="text-white-off">Une fois votre adhésion validée, vous entrez dans un univers exclusif.</p>
                  </div>
                </ScrollAnimation>
              </div>

              <ScrollAnimation animation="fade-up" delay={0.8} className="mt-8">
                <p className="text-lg text-white-off text-center">
                  L'accès étant très restreint, nous limitons le nombre d'inscriptions afin de préserver l'exclusivité et la qualité de notre réseau.
                </p>
              </ScrollAnimation>
            </div>
          </div>
        </section>

        {/* Why Join Section */}
        <section className="py-20 bg-gray-anthracite relative">
          {/* Decorative elements */}
          <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-gold-luxurious/30 to-transparent"></div>
          <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-gold-luxurious/30 to-transparent"></div>

          <div className="container mx-auto px-4 relative z-10">
            <ScrollAnimation animation="fade-up" className="mb-12">
              <h2 className="text-4xl font-playfair font-bold text-gold-luxurious text-center">
                Pourquoi rejoindre IAFUL ?
              </h2>
            </ScrollAnimation>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 max-w-6xl mx-auto">
              <ScrollAnimation animation="zoom-in" delay={0.2}>
                <div className="card hover:bg-gray-anthracite/80 hover:border hover:border-gold-luxurious/30 transition-all duration-300 h-full">
                  <div className="flex justify-center mb-4">
                    <div className="w-16 h-16 rounded-full bg-gold-luxurious/10 flex items-center justify-center">
                      <svg className="w-8 h-8 text-gold-luxurious" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"></path>
                      </svg>
                    </div>
                  </div>
                  <h4 className="text-xl font-playfair text-gold-luxurious mb-4 text-center">Un cercle fermé et sélectif</h4>
                  <p className="text-white-off text-center">Seuls les membres parrainés peuvent entrer.</p>
                </div>
              </ScrollAnimation>

              <ScrollAnimation animation="zoom-in" delay={0.3}>
                <div className="card hover:bg-gray-anthracite/80 hover:border hover:border-gold-luxurious/30 transition-all duration-300 h-full">
                  <div className="flex justify-center mb-4">
                    <div className="w-16 h-16 rounded-full bg-gold-luxurious/10 flex items-center justify-center">
                      <svg className="w-8 h-8 text-gold-luxurious" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                      </svg>
                    </div>
                  </div>
                  <h4 className="text-xl font-playfair text-gold-luxurious mb-4 text-center">Une expérience unique</h4>
                  <p className="text-white-off text-center">Avantages et services réservés à nos adhérents.</p>
                </div>
              </ScrollAnimation>

              <ScrollAnimation animation="zoom-in" delay={0.4}>
                <div className="card hover:bg-gray-anthracite/80 hover:border hover:border-gold-luxurious/30 transition-all duration-300 h-full">
                  <div className="flex justify-center mb-4">
                    <div className="w-16 h-16 rounded-full bg-gold-luxurious/10 flex items-center justify-center">
                      <svg className="w-8 h-8 text-gold-luxurious" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                      </svg>
                    </div>
                  </div>
                  <h4 className="text-xl font-playfair text-gold-luxurious mb-4 text-center">Un réseau d'exception</h4>
                  <p className="text-white-off text-center">Des connexions exclusives avec des membres triés sur le volet.</p>
                </div>
              </ScrollAnimation>

              <ScrollAnimation animation="zoom-in" delay={0.5}>
                <div className="card hover:bg-gray-anthracite/80 hover:border hover:border-gold-luxurious/30 transition-all duration-300 h-full">
                  <div className="flex justify-center mb-4">
                    <div className="w-16 h-16 rounded-full bg-gold-luxurious/10 flex items-center justify-center">
                      <svg className="w-8 h-8 text-gold-luxurious" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                      </svg>
                    </div>
                  </div>
                  <h4 className="text-xl font-playfair text-gold-luxurious mb-4 text-center">Une discrétion absolue</h4>
                  <p className="text-white-off text-center">IAFUL garantit confidentialité et sécurité à tous ses membres.</p>
                </div>
              </ScrollAnimation>
            </div>
          </div>
        </section>

        {/* Help & Resources Section */}
        <section className="py-20 bg-gray-anthracite relative">
          <div className="container mx-auto px-4 relative z-10">
            <ScrollAnimation animation="fade-up" className="mb-12">
              <h2 className="text-4xl font-playfair font-bold text-gold-luxurious text-center">
                Aide & Ressources
              </h2>
            </ScrollAnimation>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto">
              <ScrollAnimation animation="fade-right" delay={0.2}>
                <Link href="/faq" className="group">
                  <div className="card hover:bg-gray-anthracite/80 hover:border hover:border-gold-luxurious/50 transition-all duration-300 h-full transform hover:-translate-y-2">
                    <div className="flex items-center mb-6">
                      <div className="w-16 h-16 rounded-full bg-gold-luxurious/10 flex items-center justify-center mr-4 group-hover:bg-gold-luxurious/20 transition-colors duration-300">
                        <svg className="w-8 h-8 text-gold-luxurious" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                      <h3 className="text-2xl font-playfair text-gold-luxurious group-hover:text-gold-warm transition-colors duration-300">
                        FAQ
                      </h3>
                    </div>
                    <p className="text-white-off/90 leading-relaxed mb-4">
                      Trouvez rapidement les réponses à vos questions les plus courantes sur les livraisons,
                      les commandes, le système de parrainage et la sécurité.
                    </p>
                    <div className="flex items-center text-gold-luxurious group-hover:text-gold-warm transition-colors duration-300">
                      <span className="font-medium">Consulter la FAQ</span>
                      <svg className="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </Link>
              </ScrollAnimation>

              <ScrollAnimation animation="fade-left" delay={0.4}>
                <Link href="/tutorial" className="group">
                  <div className="card hover:bg-gray-anthracite/80 hover:border hover:border-gold-luxurious/50 transition-all duration-300 h-full transform hover:-translate-y-2">
                    <div className="flex items-center mb-6">
                      <div className="w-16 h-16 rounded-full bg-gold-luxurious/10 flex items-center justify-center mr-4 group-hover:bg-gold-luxurious/20 transition-colors duration-300">
                        <svg className="w-8 h-8 text-gold-luxurious" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="1.5" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                        </svg>
                      </div>
                      <h3 className="text-2xl font-playfair text-gold-luxurious group-hover:text-gold-warm transition-colors duration-300">
                        Guide Crypto
                      </h3>
                    </div>
                    <p className="text-white-off/90 leading-relaxed mb-4">
                      Apprenez à utiliser les cryptomonnaies en toute sécurité avec notre guide complet.
                      De l'installation d'un portefeuille à votre premier achat.
                    </p>
                    <div className="flex items-center text-gold-luxurious group-hover:text-gold-warm transition-colors duration-300">
                      <span className="font-medium">Voir le guide</span>
                      <svg className="w-5 h-5 ml-2 transform group-hover:translate-x-1 transition-transform duration-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </Link>
              </ScrollAnimation>
            </div>
          </div>
        </section>

        {/* Final CTA Section */}
        <section className="py-20 bg-black-deep relative">
          {/* Decorative elements */}
          <div className="absolute inset-0 overflow-hidden pointer-events-none">
            <div className="absolute -top-20 -right-20 w-80 h-80 rounded-full bg-gold-luxurious/5 blur-3xl"></div>
            <div className="absolute -bottom-20 -left-20 w-80 h-80 rounded-full bg-gold-luxurious/5 blur-3xl"></div>
          </div>

          <div className="container mx-auto px-4 text-center relative z-10">
            <ScrollAnimation animation="fade-up" className="mb-8">
              <h2 className="text-4xl font-playfair font-bold text-gold-luxurious">
                Prêt à entrer dans un univers exclusif ?
              </h2>
            </ScrollAnimation>

            <ScrollAnimation animation="fade-up" delay={0.2} className="mb-10">
              <p className="text-xl text-white-off max-w-2xl mx-auto">
                Trouvez un parrain et obtenez votre NCM pour rejoindre IAFUL dès aujourd'hui.
              </p>
            </ScrollAnimation>

            <ScrollAnimation animation="zoom-in" delay={0.4}>
              <Link
                href="/register"
                className="btn text-lg px-8 py-3 inline-block relative overflow-hidden group"
              >
                <span className="relative z-10">Commencer l'inscription</span>
                <span className="absolute inset-0 bg-gold-luxurious/30 transform scale-x-0 group-hover:scale-x-100 transition-transform origin-left duration-300"></span>
              </Link>
            </ScrollAnimation>
          </div>
        </section>
      </main>

      <footer className="bg-gray-anthracite py-8">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <span className="text-xl font-playfair font-bold text-gold-luxurious">IAFUL</span>
            </div>
            <div className="text-white-off text-sm">
              &copy; {new Date().getFullYear()} IAFUL. Tous droits réservés.
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
