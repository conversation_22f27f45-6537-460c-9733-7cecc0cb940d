import React from 'react';

type BackgroundImageProps = {
  opacity?: number;
  imagePath?: string;
};

/**
 * A component that adds a background image with overlay to any page
 * @param opacity The opacity of the overlay (0-100)
 * @param imagePath The path to the background image
 */
export default function BackgroundImage({ 
  opacity = 80, 
  imagePath = '/background.jpg' 
}: BackgroundImageProps) {
  // Convert opacity to a decimal between 0 and 1
  const opacityValue = opacity / 100;
  
  return (
    <div className="fixed inset-0 z-0">
      <div 
        className="absolute inset-0 bg-black-deep z-10"
        style={{ opacity: opacityValue }}
      ></div>
      <div 
        className="absolute inset-0 bg-cover bg-center bg-no-repeat z-0" 
        style={{ backgroundImage: `url(${imagePath})` }}
      ></div>
    </div>
  );
}
