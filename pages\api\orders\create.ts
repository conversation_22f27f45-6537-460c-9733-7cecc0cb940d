import { NextApiRequest, NextApiResponse } from 'next';
import { supabase } from '../../../lib/supabase';
import { v4 as uuidv4 } from 'uuid';

export default async function handler(req: NextApiRequest, res: NextApiResponse) {
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    const { product_id, member_id, amount_eur, applied_discount } = req.body;

    if (!product_id || !amount_eur) {
      return res.status(400).json({ error: 'Missing required fields' });
    }

    // Verify product exists
    const { data: product, error: productError } = await supabase
      .from('products')
      .select('*')
      .eq('id', product_id)
      .single();

    if (productError || !product) {
      return res.status(404).json({ error: 'Product not found' });
    }

    // Verify member exists (if provided)
    let member = null;
    if (member_id) {
      const { data: memberData, error: memberError } = await supabase
        .from('members')
        .select('*')
        .eq('id', member_id)
        .single();

      if (memberError) {
        return res.status(404).json({ error: 'Member not found' });
      }
      member = memberData;
    }

    // Verify discount if applied
    let discount = null;
    if (applied_discount) {
      const { data: discountData, error: discountError } = await supabase
        .from('coupons')
        .select('*')
        .eq('id', applied_discount)
        .eq('used', false)
        .single();

      if (discountError) {
        return res.status(400).json({ error: 'Invalid or used discount' });
      }
      discount = discountData;

      // Verify discount belongs to member (if member is exclusive)
      if (member && member.is_exclusive && discount.assigned_to_member_id !== member.id) {
        return res.status(400).json({ error: 'Discount not available for this member' });
      }
    }

    // Create order
    const orderId = uuidv4();
    const { data: order, error: orderError } = await supabase
      .from('orders')
      .insert({
        id: orderId,
        member_id: member_id,
        product_id: product_id,
        amount_eur: amount_eur,
        discount_applied: applied_discount,
        status: 'pending',
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (orderError) {
      console.error('Error creating order:', orderError);
      return res.status(500).json({ error: 'Failed to create order' });
    }

    // Log order creation
    await supabase
      .from('payment_logs')
      .insert({
        order_id: orderId,
        event_type: 'created',
        transaction_data: {
          product_id,
          member_id,
          amount_eur,
          applied_discount
        },
        created_at: new Date().toISOString()
      });

    return res.status(201).json(order);

  } catch (error) {
    console.error('Create order error:', error);
    return res.status(500).json({ error: 'Internal server error' });
  }
}
