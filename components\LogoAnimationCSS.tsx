import React, { useState, useEffect } from 'react';
import Image from 'next/image';

interface ParticleProps {
  top: string;
  left: string;
  size: string;
  delay: string;
  duration: string;
  animation: string;
  opacity: number;
}

const animations = [
  'animate-float',
  'animate-float-reverse',
  'animate-float-horizontal',
  'animate-twinkle'
];

const Particle: React.FC<ParticleProps> = ({ top, left, size, delay, duration, animation, opacity }) => {
  return (
    <div
      className={`absolute rounded-full ${animation}`}
      style={{
        top,
        left,
        width: size,
        height: size,
        animationDelay: delay,
        animationDuration: duration,
        backgroundColor: `rgba(207, 175, 90, ${opacity})`,
        boxShadow: `0 0 ${parseInt(size) * 2}px rgba(207, 175, 90, ${opacity / 2})`,
      }}
    />
  );
};

export default function LogoAnimationCSS() {
  const [particles, setParticles] = useState<ParticleProps[]>([]);
  const [mounted, setMounted] = useState(false);

  // Generate random particles
  useEffect(() => {
    setMounted(true);
    const newParticles = [];
    const particleCount = 80; // Increased particle count

    for (let i = 0; i < particleCount; i++) {
      const size = Math.random() * 12 + 2;
      newParticles.push({
        top: `${Math.random() * 100}%`,
        left: `${Math.random() * 100}%`,
        size: `${size}px`,
        delay: `${Math.random() * 5}s`,
        duration: `${Math.random() * 10 + 5}s`,
        animation: animations[Math.floor(Math.random() * animations.length)],
        opacity: Math.random() * 0.5 + 0.2,
      });
    }

    setParticles(newParticles);
  }, []);

  if (!mounted) return null;

  return (
    <div className="w-full h-full absolute inset-0 overflow-hidden">
      {/* Background particles */}
      {particles.map((particle, index) => (
        <Particle key={index} {...particle} />
      ))}

      {/* Rotating logo background effect */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-80 h-80 animate-pulse-gold">
        <div className="w-full h-full animate-spin-slow-reverse">
          <div className="w-full h-full relative">
            <Image
              src="/logo.jpg"
              alt="IAFUL Logo Background"
              fill
              className="object-contain rounded-full opacity-20 blur-sm"
            />
          </div>
        </div>
      </div>

      {/* Additional decorative elements */}
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 border border-gold-luxurious/20 rounded-full animate-spin-slow"></div>
      <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 border border-gold-luxurious/30 rounded-full animate-spin-slow-reverse"></div>
    </div>
  );
}
