import Head from 'next/head';
import { useState, useEffect } from 'react';
import { mockDB } from '../lib/mockAuth';
import Layout from '../components/Layout';

interface Product {
  id: string;
  name: string;
  description: string;
  image_url: string;
  price_eur: number;
  price_btc: number;
  available: boolean;
}

export default function ProductShowcase() {
  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadProducts = async () => {
      try {
        const productList = await mockDB.getProducts(true); // Only available products
        setProducts(productList);
      } catch (error) {
        console.error('Error loading products:', error);
      } finally {
        setLoading(false);
      }
    };

    loadProducts();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-black-deep flex items-center justify-center">
        <div className="text-gold-luxurious text-xl">Chargement des produits...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black-deep">
      <Head>
        <title>Collection Exclusive | IAFUL</title>
        <meta name="description" content="Découvrez notre collection exclusive de produits premium" />
      </Head>

      <Layout title="Collection Exclusive | IAFUL">
        <div className="container mx-auto px-4 py-12">
          {/* Header Section */}
          <div className="text-center mb-16">
            <h1 className="text-5xl font-playfair font-bold text-gold-luxurious mb-6">
              Collection Exclusive
            </h1>
            <div className="w-24 h-1 bg-gradient-to-r from-gold-luxurious to-gold-warm mx-auto mb-6"></div>
            <p className="text-xl text-white-off/80 max-w-3xl mx-auto leading-relaxed">
              Découvrez notre sélection raffinée de produits d'exception, 
              chacun choisi pour son caractère unique et sa qualité irréprochable.
            </p>
          </div>

          {/* Products Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 max-w-6xl mx-auto">
            {products.map((product, index) => (
              <div 
                key={product.id}
                className="group relative overflow-hidden rounded-2xl bg-gradient-to-br from-gray-anthracite/50 to-black-deep/80 backdrop-blur-sm border border-gold-luxurious/20 hover:border-gold-luxurious/40 transition-all duration-500 hover:transform hover:scale-[1.02]"
              >
                {/* Product Image */}
                <div className="relative h-80 overflow-hidden">
                  <div className="absolute inset-0 bg-gradient-to-t from-black-deep/80 via-transparent to-transparent z-10"></div>
                  <img
                    src={product.image_url}
                    alt={product.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                    onError={(e) => {
                      (e.target as HTMLImageElement).src = '/placeholder-product.jpg';
                    }}
                  />
                  
                  {/* Price Badge */}
                  <div className="absolute top-6 right-6 z-20">
                    <div className="bg-gold-luxurious/90 backdrop-blur-sm text-black-deep px-4 py-2 rounded-full font-bold">
                      {product.price_eur}€
                    </div>
                  </div>
                </div>

                {/* Product Content */}
                <div className="p-8">
                  <h3 className="text-3xl font-playfair font-bold text-gold-luxurious mb-4 group-hover:text-gold-warm transition-colors duration-300">
                    {product.name}
                  </h3>
                  
                  <div className="w-16 h-0.5 bg-gradient-to-r from-gold-luxurious to-transparent mb-6"></div>
                  
                  <p className="text-white-off/90 leading-relaxed text-lg mb-6 line-height-loose">
                    {product.description}
                  </p>

                  {/* Pricing Details */}
                  <div className="flex items-center justify-between pt-6 border-t border-gold-luxurious/20">
                    <div className="space-y-1">
                      <div className="text-2xl font-bold text-gold-luxurious">
                        {product.price_eur}€
                      </div>
                      <div className="text-sm text-white-off/60">
                        {product.price_btc.toFixed(8)} BTC
                      </div>
                    </div>
                    
                    <button className="bg-gradient-to-r from-gold-luxurious to-gold-warm text-black-deep px-6 py-3 rounded-full font-semibold hover:shadow-lg hover:shadow-gold-luxurious/25 transition-all duration-300 hover:transform hover:scale-105">
                      Découvrir
                    </button>
                  </div>
                </div>

                {/* Decorative Elements */}
                <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-transparent via-gold-luxurious to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            ))}
          </div>

          {/* Bottom Section */}
          <div className="text-center mt-20">
            <div className="inline-block p-8 rounded-2xl bg-gradient-to-br from-gray-anthracite/30 to-black-deep/50 backdrop-blur-sm border border-gold-luxurious/20">
              <h3 className="text-2xl font-playfair text-gold-luxurious mb-4">
                Expérience Premium
              </h3>
              <p className="text-white-off/80 max-w-2xl">
                Chaque produit de notre collection est le fruit d'un savoir-faire artisanal 
                et d'une sélection rigoureuse pour vous offrir une expérience d'exception.
              </p>
            </div>
          </div>
        </div>
      </Layout>

      <style jsx>{`
        .line-height-loose {
          line-height: 1.8;
        }
      `}</style>
    </div>
  );
}
