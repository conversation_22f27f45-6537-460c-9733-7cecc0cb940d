/**
 * IAFUL Admin Setup Script
 * Creates the initial admin user with specified credentials
 * 
 * Usage: node scripts/setup-admin.js
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

// Admin credentials
const ADMIN_EMAIL = '<EMAIL>';
const ADMIN_PASSWORD = '152dh#HF47ev@2';
const ADMIN_NAME = 'Hassen Admin';

async function setupAdmin() {
  console.log('🚀 Starting IAFUL Admin Setup...\n');

  // Initialize Supabase client
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

  if (!supabaseUrl || !supabaseServiceKey) {
    console.error('❌ Error: Missing Supabase environment variables');
    console.log('Please ensure you have:');
    console.log('- NEXT_PUBLIC_SUPABASE_URL in .env.local');
    console.log('- SUPABASE_SERVICE_ROLE_KEY in .env.local');
    process.exit(1);
  }

  const supabase = createClient(supabaseUrl, supabaseServiceKey, {
    auth: {
      autoRefreshToken: false,
      persistSession: false
    }
  });

  try {
    console.log('1. Creating admin user in Supabase Auth...');
    
    // Create the admin user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: ADMIN_EMAIL,
      password: ADMIN_PASSWORD,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        name: ADMIN_NAME,
        role: 'admin'
      }
    });

    if (authError) {
      // Check if user already exists
      if (authError.message.includes('already registered')) {
        console.log('⚠️  Admin user already exists in Auth, continuing...');
        
        // Get existing user
        const { data: existingUsers, error: listError } = await supabase.auth.admin.listUsers();
        if (listError) throw listError;
        
        const existingUser = existingUsers.users.find(u => u.email === ADMIN_EMAIL);
        if (!existingUser) {
          throw new Error('Could not find existing admin user');
        }
        
        authData.user = existingUser;
      } else {
        throw authError;
      }
    } else {
      console.log('✅ Admin user created successfully in Auth');
    }

    const adminUserId = authData.user.id;
    console.log(`   User ID: ${adminUserId}`);

    console.log('\n2. Creating admin member record...');
    
    // Create or update the member record
    const { data: memberData, error: memberError } = await supabase
      .from('members')
      .upsert({
        auth_id: adminUserId,
        name: ADMIN_NAME,
        email: ADMIN_EMAIL,
        level: 0, // Admin level
        referrer_id: null,
        ncm_key_used: 'ADMIN-INIT-001',
        is_admin: true,
        is_exclusive: false,
        active: true,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'auth_id'
      })
      .select();

    if (memberError) {
      throw memberError;
    }

    console.log('✅ Admin member record created/updated successfully');

    console.log('\n3. Setting up admin settings...');
    
    // Create admin settings table if it doesn't exist
    const { error: tableError } = await supabase.rpc('create_admin_settings_table');
    
    // Insert default admin settings
    const defaultSettings = [
      {
        setting_key: 'site_name',
        setting_value: 'IAFUL',
        description: 'Site name for the application'
      },
      {
        setting_key: 'admin_email',
        setting_value: ADMIN_EMAIL,
        description: 'Primary admin email'
      },
      {
        setting_key: 'max_ncm_generation',
        setting_value: '100',
        description: 'Maximum NCM keys that can be generated at once'
      },
      {
        setting_key: 'exclusive_member_limit',
        setting_value: '1000',
        description: 'Maximum number of exclusive members'
      }
    ];

    for (const setting of defaultSettings) {
      const { error: settingError } = await supabase
        .from('admin_settings')
        .upsert({
          ...setting,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'setting_key'
        });

      if (settingError && !settingError.message.includes('does not exist')) {
        console.log(`⚠️  Could not create setting ${setting.setting_key}: ${settingError.message}`);
      }
    }

    console.log('✅ Admin settings configured');

    console.log('\n4. Verifying admin setup...');
    
    // Verify the admin user
    const { data: verifyData, error: verifyError } = await supabase
      .from('members')
      .select('*')
      .eq('email', ADMIN_EMAIL)
      .eq('is_admin', true)
      .single();

    if (verifyError) {
      throw verifyError;
    }

    console.log('✅ Admin verification successful');

    console.log('\n🎉 IAFUL Admin Setup Completed Successfully!');
    console.log('\n📋 Admin Credentials:');
    console.log(`   Email: ${ADMIN_EMAIL}`);
    console.log(`   Password: ${ADMIN_PASSWORD}`);
    console.log(`   Name: ${ADMIN_NAME}`);
    console.log(`   User ID: ${adminUserId}`);
    
    console.log('\n🔐 Security Notes:');
    console.log('   • Change the admin password after first login');
    console.log('   • Access admin panel at: /admin');
    console.log('   • Update settings at: /admin/settings');
    
    console.log('\n✅ You can now login to the admin panel!');

  } catch (error) {
    console.error('\n❌ Error during admin setup:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Check your Supabase environment variables');
    console.log('2. Ensure your Supabase project is properly configured');
    console.log('3. Verify database tables exist (run migrations if needed)');
    console.log('4. Check Supabase dashboard for any errors');
    process.exit(1);
  }
}

// Create admin settings table function (SQL)
async function createAdminSettingsTable(supabase) {
  const { error } = await supabase.rpc('exec_sql', {
    sql: `
      CREATE TABLE IF NOT EXISTS admin_settings (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        setting_key VARCHAR(255) UNIQUE NOT NULL,
        setting_value TEXT NOT NULL,
        description TEXT,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
      );
      
      CREATE INDEX IF NOT EXISTS idx_admin_settings_key ON admin_settings(setting_key);
    `
  });
  
  return error;
}

// Run the setup
if (require.main === module) {
  setupAdmin().catch(console.error);
}

module.exports = { setupAdmin };
