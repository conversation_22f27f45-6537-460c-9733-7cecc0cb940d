import Head from 'next/head';
import Navbar from './Navbar';
import Footer from './Footer';
import BackgroundImage from './BackgroundImage';

type LayoutProps = {
  children: React.ReactNode;
  title?: string;
  isAdmin?: boolean;
  backgroundOpacity?: number;
  backgroundImage?: string;
};

export default function Layout({
  children,
  title = 'IAFUL',
  isAdmin = false,
  backgroundOpacity = 80,
  backgroundImage = '/background.jpg'
}: LayoutProps) {
  return (
    <div className="min-h-screen bg-black-deep relative">
      {/* Background image component */}
      <BackgroundImage opacity={backgroundOpacity} imagePath={backgroundImage} />

      <Head>
        <title>{title}</title>
        <meta name="description" content="IAFUL - Le Club Privé Réservé à une Élite Sélectionnée" />
        <link rel="icon" href="/favicon.ico" />
      </Head>

      <Navbar isAdmin={isAdmin} />

      <main className="pt-24 pb-20 relative z-10">
        {children}
      </main>

      <Footer isAdmin={isAdmin} />
    </div>
  );
}
