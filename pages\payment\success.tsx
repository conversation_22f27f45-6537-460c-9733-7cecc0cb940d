import { useState, useEffect, useContext } from 'react';
import { useRouter } from 'next/router';
import Link from 'next/link';
import Layout from '../../components/Layout';
import { AuthContext, supabase } from '../_app';

export default function PaymentSuccess() {
  const router = useRouter();
  const { user } = useContext(AuthContext);
  const { order: orderId } = router.query;
  const [loading, setLoading] = useState(true);
  const [order, setOrder] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchOrderDetails = async () => {
      if (!orderId) return;

      try {
        const { data: orderData, error: orderError } = await supabase
          .from('orders')
          .select(`
            *,
            member:members(*),
            product:products(*),
            discount:coupons(*)
          `)
          .eq('id', orderId)
          .single();

        if (orderError) {
          throw new Error('Order not found');
        }

        setOrder(orderData);
      } catch (error: any) {
        setError(error.message);
      } finally {
        setLoading(false);
      }
    };

    fetchOrderDetails();
  }, [orderId]);

  if (loading) {
    return (
      <Layout title="Paiement en cours | IAFUL">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gold-luxurious mx-auto mb-4"></div>
              <div className="text-gold-luxurious text-xl">Vérification du paiement...</div>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  if (error || !order) {
    return (
      <Layout title="Erreur de Paiement | IAFUL">
        <div className="container mx-auto px-4">
          <div className="max-w-2xl mx-auto text-center py-12">
            <div className="text-red-400 text-6xl mb-6">❌</div>
            <h1 className="text-3xl font-playfair font-bold text-white-off mb-4">
              Erreur de Paiement
            </h1>
            <p className="text-white-off/70 mb-8">
              {error || 'Impossible de trouver les détails de votre commande.'}
            </p>
            <div className="space-y-4">
              <Link href="/products" className="btn inline-block">
                Retour aux Produits
              </Link>
              <Link href="/profile" className="btn-secondary inline-block ml-4">
                Mon Profil
              </Link>
            </div>
          </div>
        </div>
      </Layout>
    );
  }

  const isPaymentSuccessful = order.status === 'paid' || order.status === 'fulfilled';

  return (
    <Layout title="Paiement Confirmé | IAFUL">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto py-12">
          {isPaymentSuccessful ? (
            <>
              {/* Success Header */}
              <div className="text-center mb-12">
                <div className="text-green-400 text-6xl mb-6">✅</div>
                <h1 className="text-4xl font-playfair font-bold text-gold-luxurious mb-4">
                  Paiement Confirmé !
                </h1>
                <p className="text-white-off/80 text-lg">
                  Votre commande a été traitée avec succès. Merci pour votre achat !
                </p>
              </div>

              {/* Order Details */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                {/* Order Summary */}
                <div className="card">
                  <h2 className="text-xl font-playfair text-gold-luxurious mb-6">
                    Détails de la Commande
                  </h2>
                  
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-white-off/70">Numéro de commande:</span>
                      <span className="text-white-off font-mono text-sm">
                        {order.id.slice(0, 8)}...
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-white-off/70">Produit:</span>
                      <span className="text-gold-luxurious font-medium">
                        {order.product?.name}
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-white-off/70">Prix:</span>
                      <span className="text-white-off">
                        €{order.amount_eur}
                      </span>
                    </div>
                    
                    {order.discount && (
                      <div className="flex justify-between items-center">
                        <span className="text-white-off/70">Réduction:</span>
                        <span className="text-green-400">
                          -{order.discount.discount_pct}% ({order.discount.code})
                        </span>
                      </div>
                    )}
                    
                    <div className="flex justify-between items-center">
                      <span className="text-white-off/70">Statut:</span>
                      <span className="px-2 py-1 bg-green-900/30 text-green-400 text-sm rounded">
                        {order.status === 'fulfilled' ? 'Livré' : 'Payé'}
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-white-off/70">Date:</span>
                      <span className="text-white-off">
                        {new Date(order.paid_at || order.created_at).toLocaleDateString('fr-FR')}
                      </span>
                    </div>
                  </div>
                </div>

                {/* Payment Details */}
                <div className="card">
                  <h2 className="text-xl font-playfair text-gold-luxurious mb-6">
                    Détails du Paiement
                  </h2>
                  
                  <div className="space-y-4">
                    <div className="flex justify-between items-center">
                      <span className="text-white-off/70">Blockchain:</span>
                      <span className="text-white-off capitalize">
                        {order.blockchain || 'Ethereum'}
                      </span>
                    </div>
                    
                    <div className="flex justify-between items-center">
                      <span className="text-white-off/70">Montant crypto:</span>
                      <span className="text-white-off">
                        {order.amount_crypto || 'N/A'}
                      </span>
                    </div>
                    
                    {order.transaction_hash && (
                      <div className="space-y-2">
                        <span className="text-white-off/70">Transaction:</span>
                        <div className="bg-gray-anthracite p-3 rounded-md">
                          <span className="text-white-off font-mono text-xs break-all">
                            {order.transaction_hash}
                          </span>
                        </div>
                        <a
                          href={`https://etherscan.io/tx/${order.transaction_hash}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="text-gold-luxurious hover:underline text-sm"
                        >
                          Voir sur Etherscan →
                        </a>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Member Information */}
              {order.member && (
                <div className="card mb-12">
                  <h2 className="text-xl font-playfair text-gold-luxurious mb-6">
                    Informations du Membre
                  </h2>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-gradient-to-r from-gold-luxurious to-gold-warm rounded-full flex items-center justify-center text-black-deep font-bold mr-4">
                        {order.member.name?.charAt(0) || 'M'}
                      </div>
                      <div>
                        <div className="text-white-off font-medium">
                          {order.member.name}
                        </div>
                        <div className="text-white-off/70 text-sm">
                          {order.member.email}
                        </div>
                      </div>
                    </div>
                    
                    {order.member.is_exclusive && (
                      <span className="px-3 py-1 bg-gradient-to-r from-gold-luxurious to-gold-warm text-black-deep text-sm font-medium rounded-full">
                        Membre Exclusif
                      </span>
                    )}
                  </div>
                </div>
              )}

              {/* Next Steps */}
              <div className="bg-gradient-to-r from-gold-luxurious/10 to-gold-warm/10 border border-gold-luxurious/30 rounded-lg p-6 mb-8">
                <h3 className="text-gold-luxurious font-medium mb-4">🎉 Prochaines Étapes</h3>
                <ul className="text-white-off/80 space-y-2">
                  <li>• Votre produit est maintenant disponible dans votre profil</li>
                  <li>• Vous recevrez un email de confirmation sous peu</li>
                  <li>• L'accès au produit est permanent pour votre compte</li>
                  {order.member?.is_exclusive && (
                    <li>• Vos coupons exclusifs restent disponibles pour vos prochains achats</li>
                  )}
                </ul>
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link href="/products" className="btn text-center py-3">
                  Continuer mes Achats
                </Link>
                <Link href="/profile" className="btn-secondary text-center py-3">
                  Voir mon Profil
                </Link>
                {order.member && (
                  <Link href="/orders" className="btn-secondary text-center py-3">
                    Mes Commandes
                  </Link>
                )}
              </div>
            </>
          ) : (
            <>
              {/* Payment Pending */}
              <div className="text-center mb-12">
                <div className="text-yellow-400 text-6xl mb-6">⏳</div>
                <h1 className="text-4xl font-playfair font-bold text-gold-luxurious mb-4">
                  Paiement en Cours
                </h1>
                <p className="text-white-off/80 text-lg">
                  Votre paiement est en cours de traitement. Veuillez patienter...
                </p>
              </div>

              <div className="card text-center">
                <p className="text-white-off mb-6">
                  Le traitement peut prendre quelques minutes selon la blockchain utilisée.
                </p>
                <button
                  onClick={() => window.location.reload()}
                  className="btn"
                >
                  Actualiser la Page
                </button>
              </div>
            </>
          )}
        </div>
      </div>
    </Layout>
  );
}
